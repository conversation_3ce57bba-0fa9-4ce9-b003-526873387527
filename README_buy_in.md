# 股票购买方案计算器

这是一个用于计算股票购买方案的Python脚本，可以根据给定的总资金自动获取实时股票价格，并计算出最优的股票购买分配方案。

## 功能特点

- **智能分配算法**: 平均分配资金后，将剩余资金优先分配给价格较低的股票，最大化资金利用率
- **实时数据获取**: 使用mootdx API自动获取实时股票价格
- **详细报告**: 显示每只股票的购买股数、投入金额、总投入、剩余资金和资金利用率
- **简单易用**: 只需修改股票代码列表和总资金即可使用

## 安装依赖

```bash
pip install mootdx
```

## 使用方法

### 1. 配置股票和资金

编辑 `buy_in.py` 文件中的配置：

```python
# 修改股票列表
stock_list = ['000001', '000002', '600036', '600519']  # 替换为你关注的股票代码

# 修改总资金
total_capital = 20000  # 替换为你的总资金
```

### 2. 运行脚本

```bash
python buy_in.py
```

### 3. 示例输出

```
欢迎使用股票购买方案计算器
当前配置:
股票列表: 000001, 000002, 600036, 600519
总资金: 20000元

开始计算...
股票购买方案计算器
==================================================
总资金: 20000.00元
股票数量: 4只
==================================================
正在获取股票价格...
获取 000001 价格中...
✓ 000001: 12.39元
获取 000002 价格中...
✓ 000002: 6.78元
获取 600036 价格中...
✓ 600036: 44.90元
获取 600519 价格中...
✓ 600519: 1462.59元

成功获取 4 只股票价格，开始计算购买方案...

============================================================
股票购买方案
============================================================
股票代码	价格(元)	股数	投入金额(元)
------------------------------------------------------------
000001		12.39		403	4993.17
000002		6.78		831	5634.18
600036		44.90		111	4983.90
600519		1462.59		3	4387.77
------------------------------------------------------------
总投入金额：19999.02元
剩余资金：0.98元
资金利用率：100.0%
============================================================
```

## 算法说明

1. **初始分配**: 将总资金平均分配给所有股票
2. **股数计算**: 根据每只股票的价格计算可购买的股数（向下取整）
3. **剩余分配**: 将剩余资金按股票价格从低到高的顺序进行分配
4. **优化目标**: 最大化资金利用率，尽可能减少剩余资金

## 注意事项

1. **股票代码格式**: 请使用6位数字的股票代码（如：000001、600519）
2. **网络连接**: 需要网络连接来获取实时股票数据
3. **市场时间**: 实时数据获取可能受到市场开放时间影响
4. **数据准确性**: 如果无法获取实时价格，程序会尝试使用最新的收盘价

## 支持的股票市场

- 上海证券交易所（股票代码以6开头）
- 深圳证券交易所（股票代码以0、2、3开头）

## 技术实现

- **数据源**: 使用mootdx库获取实时行情数据
- **备用方案**: 如果实时数据获取失败，自动使用最新K线收盘价
- **算法优化**: 智能分配剩余资金，最大化资金利用率

## 许可证

本项目仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。
