#!/usr/bin/env python3
"""
测试邮件中的沪深300标注功能
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List

# 添加当前目录到路径
sys.path.append('.')

from Selector import BBIKDJSelector

def create_mock_data():
    """创建模拟数据"""
    # 创建模拟的股票数据
    dates = pd.date_range('2025-07-01', '2025-07-28', freq='D')
    mock_data = {}
    
    # 模拟几只股票的数据
    stocks = ["000001", "600000", "300750", "688981"]
    
    for stock in stocks:
        df = pd.DataFrame({
            'date': dates,
            'open': [10.0 + i * 0.1 for i in range(len(dates))],
            'close': [10.1 + i * 0.1 for i in range(len(dates))],
            'high': [10.2 + i * 0.1 for i in range(len(dates))],
            'low': [9.9 + i * 0.1 for i in range(len(dates))],
            'volume': [1000000] * len(dates)
        })
        mock_data[stock] = df
    
    return mock_data

def test_email_generation():
    """测试邮件生成功能"""
    print("=" * 80)
    print("测试邮件中的沪深300标注功能")
    print("=" * 80)
    
    # 创建BBIKDJSelector实例
    selector = BBIKDJSelector()
    
    # 创建模拟数据
    data = create_mock_data()
    date = pd.Timestamp("2025-07-28")
    
    # 模拟选中的股票
    picks = ["000001", "600000", "300750"]
    
    print(f"模拟选中股票: {picks}")
    print()
    
    # 测试邮件内容生成
    try:
        email_content = selector._generate_email_content(picks, date, data, max_capital=20000)
        
        # 保存邮件内容到文件以便查看
        with open("test_email_output.html", "w", encoding="utf-8") as f:
            f.write(email_content)
        
        print("✅ 邮件内容生成成功！")
        print(f"邮件内容已保存到: test_email_output.html")
        print()
        
        # 检查邮件内容是否包含沪深300标注
        if "沪深300" in email_content or "✅" in email_content or "❌" in email_content:
            print("✅ 邮件内容包含沪深300标注")
        else:
            print("❌ 邮件内容不包含沪深300标注")
        
        # 显示邮件内容的关键部分
        print("\n邮件内容预览（关键部分）:")
        print("-" * 60)
        
        # 查找表格部分
        if "<table" in email_content and "</table>" in email_content:
            start = email_content.find("<table")
            end = email_content.find("</table>") + 8
            table_content = email_content[start:end]
            
            # 简化显示
            lines = table_content.split('\n')
            for line in lines[:20]:  # 只显示前20行
                if line.strip():
                    print(line.strip())
            if len(lines) > 20:
                print("... (更多内容)")
        
    except Exception as e:
        print(f"❌ 邮件内容生成失败: {e}")
        import traceback
        traceback.print_exc()

def test_purchase_plan():
    """测试购买方案计算"""
    print("\n" + "=" * 80)
    print("测试购买方案计算中的沪深300标注")
    print("=" * 80)
    
    # 创建BBIKDJSelector实例
    selector = BBIKDJSelector()
    
    # 创建模拟数据
    data = create_mock_data()
    date = pd.Timestamp("2025-07-28")
    
    # 模拟选中的股票
    picks = ["000001", "600000", "300750"]
    
    print("模拟购买方案计算输出:")
    print()
    
    try:
        # 调用购买方案计算方法
        selector._calculate_purchase_plan(picks, date, data, max_capital=20000)
        print("\n✅ 购买方案计算成功！")
    except Exception as e:
        print(f"❌ 购买方案计算失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_email_generation()
    test_purchase_plan()
