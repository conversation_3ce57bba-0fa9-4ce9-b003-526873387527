#!/usr/bin/env python3
"""
测试沪深300标注功能
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List

# 添加当前目录到路径
sys.path.append('.')

from Selector import BBIKDJSelector
from hs300_utils import format_stock_list_with_hs300, get_hs300_summary

def test_hs300_annotation():
    """测试沪深300标注功能"""
    print("=" * 80)
    print("测试沪深300标注功能")
    print("=" * 80)
    
    # 测试股票列表（包含沪深300和非沪深300股票）
    test_picks = ["000001", "600000", "300750", "688981", "123456", "000002", "600036"]
    
    print(f"测试股票列表: {test_picks}")
    print()
    
    # 测试格式化函数
    formatted_picks = format_stock_list_with_hs300(test_picks)
    print(f"格式化结果: {formatted_picks}")
    print()
    
    # 测试统计摘要
    summary = get_hs300_summary(test_picks)
    print("统计摘要:")
    print(f"  总股票数: {summary['total_count']}")
    print(f"  沪深300成分股: {summary['hs300_count']}")
    print(f"  非沪深300股票: {summary['non_hs300_count']}")
    print(f"  沪深300占比: {summary['hs300_ratio']:.1f}%")
    print()
    
    # 测试BBIKDJSelector的沪深300功能
    print("=" * 80)
    print("测试BBIKDJSelector的沪深300标注功能")
    print("=" * 80)
    
    # 创建BBIKDJSelector实例
    selector = BBIKDJSelector()
    
    # 模拟购买方案计算的输出
    print("模拟购买方案计算输出:")
    print("-" * 80)
    
    # 模拟选中股票
    picks = ["000001", "600000", "300750"]
    date = pd.Timestamp("2025-07-28")
    max_capital = 20000
    
    print(f"选中股票: {', '.join(picks)}")
    print(f"总资金: {max_capital:.2f}元")
    print(f"股票数量: {len(picks)}只")
    
    # 添加沪深300统计信息
    hs300_count = sum(1 for code in picks if selector._is_hs300_stock(code))
    non_hs300_count = len(picks) - hs300_count
    hs300_ratio = (hs300_count / len(picks)) * 100 if picks else 0
    print(f"沪深300成分股: {hs300_count}只, 非沪深300: {non_hs300_count}只, 沪深300占比: {hs300_ratio:.1f}%")
    print()
    
    # 模拟股票详细信息
    print("股票详细信息:")
    print("股票代码\t价格(元)\t股数\t投入金额(元)\t资金占比(%)\t沪深300\t股票名称")
    print("-" * 100)
    
    # 模拟数据
    mock_data = [
        ("000001", 12.46, 500, 6230.00, 31.2),
        ("600000", 8.95, 700, 6265.00, 31.3),
        ("300750", 185.50, 30, 5565.00, 27.8)
    ]
    
    for code, price, share, investment, ratio in mock_data:
        stock_name = selector._get_stock_name(code)
        hs300_label = "沪深300" if selector._is_hs300_stock(code) else "非沪深300"
        print(f"{code}\t\t{price:.2f}\t\t{share}\t{investment:.2f}\t\t{ratio:.1f}%\t[{hs300_label}] {stock_name}")
    
    print()
    print("测试完成！")

if __name__ == "__main__":
    test_hs300_annotation()
