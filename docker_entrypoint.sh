#!/bin/bash

# Docker容器启动脚本
# 用于在容器启动时替换第三方库文件并启动主程序

echo "[$(date)] Docker容器启动中..."

# 查找mootdx安装路径
MOOTDX_PATH=$(python -c "import mootdx; print(mootdx.__path__[0])" 2>/dev/null)

if [ -z "$MOOTDX_PATH" ]; then
    echo "[$(date)] 错误: 无法找到mootdx库路径"
    exit 1
fi

echo "[$(date)] 找到mootdx库路径: $MOOTDX_PATH"

# 替换reversion.py文件
REVERSION_TARGET="$MOOTDX_PATH/tools/reversion.py"
REVERSION_SOURCE="/app/reversion.py"

if [ -f "$REVERSION_SOURCE" ]; then
    echo "[$(date)] 正在替换 $REVERSION_TARGET"
    
    # 备份原文件
    if [ -f "$REVERSION_TARGET" ]; then
        cp "$REVERSION_TARGET" "$REVERSION_TARGET.backup"
        echo "[$(date)] 已备份原文件到 $REVERSION_TARGET.backup"
    fi
    
    # 替换文件
    cp "$REVERSION_SOURCE" "$REVERSION_TARGET"
    
    if [ $? -eq 0 ]; then
        echo "[$(date)] 成功替换 reversion.py 文件"
    else
        echo "[$(date)] 错误: 替换 reversion.py 文件失败"
        exit 1
    fi
else
    echo "[$(date)] 警告: 源文件 $REVERSION_SOURCE 不存在，跳过替换"
fi

# 验证替换是否成功
echo "[$(date)] 验证文件替换..."
python -c "
import mootdx.tools.reversion as rev
print('reversion.py 模块加载成功')
print('文件路径:', rev.__file__)
"

if [ $? -eq 0 ]; then
    echo "[$(date)] 文件替换验证成功"
else
    echo "[$(date)] 错误: 文件替换验证失败"
    exit 1
fi

echo "[$(date)] 启动主程序..."

# 启动主程序
exec "$@"
