# 沪深300成分股标注功能

## 功能概述

为BBIKDJSelector选股器添加了沪深300成分股标注功能，在选股结果的邮件内容和控制台输出中显示每只股票是否为沪深300成分股。

## 实现的功能

### 1. 沪深300成分股数据加载
- 自动从根目录的 `hs300_stocks.csv` 文件加载沪深300成分股列表
- 支持带交易所前缀的股票代码格式（如 `sh.600000`, `sz.000001`）
- 自动去除交易所前缀进行匹配

### 2. 控制台输出增强
在 `select_stock.py` 的选股结果中添加：
- 沪深300成分股统计信息
- 带沪深300标注的股票列表显示

示例输出：
```
============== 选股结果 [少妇战法] ==============
交易日: 2025-07-28
符合条件股票数: 3
沪深300成分股: 3只, 非沪深300: 0只, 沪深300占比: 100.0%
选中股票: 000001[沪深300], 600000[沪深300], 300750[沪深300]
```

### 3. 购买方案输出增强
在BBIKDJSelector的 `_calculate_purchase_plan` 方法中添加：
- 沪深300统计信息
- 每只股票的沪深300标注

示例输出：
```
================================================================================
股票购买方案计算 - 选股日期: 2025-07-28
================================================================================
选中股票: 000001, 600000, 300750
总资金: 20000.00元
股票数量: 3只
沪深300成分股: 3只, 非沪深300: 0只, 沪深300占比: 100.0%
================================================================================

股票代码	价格(元)	股数	投入金额(元)	资金占比(%)	沪深300	股票名称
----------------------------------------------------------------------------------------------------
000001		12.80		500	6400.00		32.0%	[沪深300] 上证指数
600000		12.80		500	6400.00		32.0%	[沪深300] 浦发银行
300750		12.80		500	6400.00		32.0%	[沪深300] 宁德时代
```

### 4. 邮件内容增强
在邮件的购买方案表格中添加"沪深300"列：
- ✅ 表示沪深300成分股
- ❌ 表示非沪深300股票

邮件表格格式：
| 股票代码 | 股票名称 | 沪深300 | 价格(元) | 股数 | 投入金额(元) | 资金占比(%) |
|---------|---------|---------|---------|------|------------|------------|
| 000001  | 平安银行 | ✅      | 12.46   | 500  | 6,230.00   | 31.2%      |
| 600000  | 浦发银行 | ✅      | 8.95    | 700  | 6,265.00   | 31.3%      |

## 新增文件

### 1. `hs300_utils.py`
独立的沪深300工具模块，提供：
- `HS300Utils` 类：完整的沪深300功能
- 便捷函数：`is_hs300_stock()`, `get_hs300_label()`, `format_stock_list_with_hs300()`, `get_hs300_summary()`
- 全局实例：`hs300_utils`

### 2. 测试文件
- `test_hs300_annotation.py`：基础功能测试
- `test_email_hs300.py`：邮件和购买方案测试

## 修改的文件

### 1. `Selector.py`
- 在 `BBIKDJSelector.__init__()` 中添加沪深300数据加载
- 添加 `_load_hs300_stocks()` 和 `_is_hs300_stock()` 方法
- 修改 `_calculate_purchase_plan()` 添加沪深300统计和标注
- 修改 `_generate_stock_content()` 在邮件表格中添加沪深300列

### 2. `select_stock.py`
- 导入 `hs300_utils` 模块
- 修改选股结果输出，添加沪深300统计和格式化显示

## 配置说明

### 沪深300数据文件
默认使用根目录的 `hs300_stocks.csv` 文件，格式要求：
```csv
updateDate,code,code_name
2025-07-28,sh.600000,浦发银行
2025-07-28,sz.000001,平安银行
...
```

### 自定义配置
可以在BBIKDJSelector初始化时指定不同的沪深300文件路径：
```python
selector = BBIKDJSelector(hs300_csv_path="custom_hs300.csv")
```

## 使用示例

### 1. 运行选股
```bash
python select_stock.py --date "2025-07-28"
```

### 2. 测试功能
```bash
# 测试基础功能
python test_hs300_annotation.py

# 测试邮件功能
python test_email_hs300.py
```

### 3. 在代码中使用
```python
from hs300_utils import is_hs300_stock, format_stock_list_with_hs300, get_hs300_summary

# 检查单只股票
is_hs300 = is_hs300_stock("000001")  # True

# 格式化股票列表
picks = ["000001", "600000", "123456"]
formatted = format_stock_list_with_hs300(picks)
# 输出: "000001[沪深300], 600000[沪深300], 123456[非沪深300]"

# 获取统计摘要
summary = get_hs300_summary(picks)
# 输出: {'total_count': 3, 'hs300_count': 2, 'non_hs300_count': 1, 'hs300_ratio': 66.7}
```

## 注意事项

1. **数据文件依赖**：确保 `hs300_stocks.csv` 文件存在且格式正确
2. **股票代码格式**：支持带或不带交易所前缀的股票代码
3. **向后兼容**：所有修改都保持了原有功能的完整性
4. **错误处理**：当沪深300文件不存在或格式错误时，会显示警告但不影响选股功能

## 效果展示

通过这些增强，用户可以：
- 在控制台快速了解选中股票的沪深300分布
- 在邮件中清晰看到每只股票的沪深300属性
- 更好地进行投资组合分析和风险评估
