# BBIKDJSelector 邮件通知功能

## 功能说明

BBIKDJSelector 现在支持在选股完成后自动发送邮件通知，包含：
- 选股结果列表（包含股票代码和名称）
- 股票价格信息
- 详细的购买方案计算
- 资金分配分析
- 精美的HTML格式邮件

## 邮件配置

### 1. 配置邮箱信息

编辑 `email_config.json` 文件：

```json
{
  "host": "smtp.qq.com",
  "port": 465,
  "auth": {
    "user": "<EMAIL>",
    "pass": "your_app_password"
  }
}
```

### 2. 常用邮箱配置

#### QQ邮箱
```json
{
  "host": "smtp.qq.com",
  "port": 465,
  "auth": {
    "user": "<EMAIL>",
    "pass": "your_app_password"
  }
}
```

#### 163邮箱
```json
{
  "host": "smtp.163.com",
  "port": 465,
  "auth": {
    "user": "<EMAIL>",
    "pass": "your_app_password"
  }
}
```

#### Gmail
```json
{
  "host": "smtp.gmail.com",
  "port": 587,
  "auth": {
    "user": "<EMAIL>",
    "pass": "your_app_password"
  }
}
```

### 3. 获取应用密码

#### QQ邮箱
1. 登录QQ邮箱网页版
2. 点击"设置" → "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"IMAP/SMTP服务"
5. 按提示获取授权码（应用密码）

#### 163邮箱
1. 登录163邮箱网页版
2. 点击"设置" → "POP3/SMTP/IMAP"
3. 开启"IMAP/SMTP服务"
4. 按提示获取授权码

#### Gmail
1. 开启两步验证
2. 生成应用专用密码
3. 使用应用专用密码作为密码

## 使用方法

### 1. 在选股器中使用

```python
from Selector import BBIKDJSelector

# 创建选股器实例（会自动启用邮件功能）
selector = BBIKDJSelector(
    j_threshold=0,
    bbi_min_window=20,
    max_window=60,
    price_range_pct=0.5,
    bbi_q_threshold=0.1,
    j_q_threshold=0.01,
    email_config_file="email_config.json"  # 可选，默认为 email_config.json
)

# 执行选股（会自动发送邮件）
picks = selector.select(date, data)
```

### 2. 邮件内容示例

邮件将以精美的HTML格式发送，包含以下内容：

**📈 BBIKDJSelector 选股结果报告**
- 📅 选股日期: 2024-07-25
- 🕒 生成时间: 2024-07-25 14:30:00
- 📊 选中股票数量: 3只
- 💰 总资金: 20,000.00元

**🎯 选中股票列表**
- 000001 (平安银行): 10.50元 (2024-07-25)
- 000002 (万科A): 25.30元 (2024-07-25)
- 600036 (招商银行): 15.80元 (2024-07-25)

**💼 购买方案详情**

| 股票代码 | 股票名称 | 价格(元) | 股数 | 投入金额(元) | 资金占比(%) |
|---------|---------|---------|------|------------|------------|
| 000001  | 平安银行 | 10.50   | 600  | 6,300.00   | 31.5%      |
| 000002  | 万科A   | 25.30   | 200  | 5,060.00   | 25.3%      |
| 600036  | 招商银行 | 15.80   | 500  | 7,900.00   | 39.5%      |

**📊 资金分配汇总**
- 💰 总投入金额: 19,260.00元
- 💵 剩余资金: 740.00元
- 📈 资金利用率: 96.3%
- ⚖️ 资金占比差异: 14.2% (最高39.5% - 最低25.3%)

**⚠️ 免责声明**
本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。

## 故障排除

### 1. 邮件发送失败

- 检查网络连接
- 确认邮箱配置正确
- 确认应用密码正确
- 检查邮箱服务商的SMTP设置

### 2. 配置文件问题

- 确保 `email_config.json` 文件存在
- 检查JSON格式是否正确
- 确认所有必需字段都已填写

### 3. 权限问题

- 确保邮箱已开启SMTP服务
- 检查是否需要开启"安全性较低的应用访问权限"
- 对于Gmail，确保使用应用专用密码

## 安全提示

1. **不要将邮箱密码提交到版本控制系统**
2. **使用应用专用密码而不是邮箱登录密码**
3. **定期更换应用密码**
4. **妥善保管配置文件**

## 禁用邮件功能

如果不需要邮件功能，可以：

1. 删除或重命名 `email_config.json` 文件
2. 或者将配置文件中的用户名设置为默认值 `<EMAIL>`

程序会自动检测并跳过邮件发送。
