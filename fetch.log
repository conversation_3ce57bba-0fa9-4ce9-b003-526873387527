2025-07-25 01:22:34,289 [INFO] fetch_kline.py:72 从网络获取市值数据...
2025-07-25 01:22:43,835 [INFO] fetch_kline.py:97 市值数据已缓存到本地 (记录数: 5734)
2025-07-25 01:22:43,841 [INFO] fetch_kline.py:127 筛选得到 2123 只股票
2025-07-25 01:22:43,850 [INFO] fetch_kline.py:442 开始抓取 2123 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250725
2025-07-25 01:22:56,194 [WARNING] fetch_kline.py:255 Mootdx 获取 000016 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:56,203 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:22:58,933 [WARNING] fetch_kline.py:255 Mootdx 获取 000006 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:58,934 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:22:59,002 [WARNING] fetch_kline.py:255 Mootdx 获取 000011 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:59,003 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:22:59,067 [WARNING] fetch_kline.py:255 Mootdx 获取 000009 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:59,068 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:22:59,100 [WARNING] fetch_kline.py:255 Mootdx 获取 000008 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:59,105 [WARNING] fetch_kline.py:255 Mootdx 获取 000021 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:59,106 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:22:59,107 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:22:59,114 [WARNING] fetch_kline.py:255 Mootdx 获取 000002 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:59,120 [WARNING] fetch_kline.py:255 Mootdx 获取 000012 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:59,126 [WARNING] fetch_kline.py:255 Mootdx 获取 000001 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:59,127 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:22:59,135 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:22:59,135 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:22:59,139 [WARNING] fetch_kline.py:255 Mootdx 获取 000019 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:22:59,151 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:06,815 [WARNING] fetch_kline.py:255 Mootdx 获取 000025 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:06,816 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:10,091 [WARNING] fetch_kline.py:255 Mootdx 获取 000026 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:10,094 [WARNING] fetch_kline.py:255 Mootdx 获取 000027 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:10,095 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:10,095 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:11,367 [WARNING] fetch_kline.py:255 Mootdx 获取 000030 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:11,368 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:12,471 [WARNING] fetch_kline.py:255 Mootdx 获取 000034 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:12,473 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:12,812 [WARNING] fetch_kline.py:255 Mootdx 获取 000031 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:12,821 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:12,914 [WARNING] fetch_kline.py:255 Mootdx 获取 000032 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:12,915 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:13,044 [WARNING] fetch_kline.py:255 Mootdx 获取 000035 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:13,045 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:13,275 [WARNING] fetch_kline.py:255 Mootdx 获取 000028 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:13,277 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:13,377 [WARNING] fetch_kline.py:255 Mootdx 获取 000029 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:13,378 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:17,581 [WARNING] fetch_kline.py:255 Mootdx 获取 000036 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:17,582 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:20,655 [WARNING] fetch_kline.py:255 Mootdx 获取 000039 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:20,656 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:20,666 [WARNING] fetch_kline.py:255 Mootdx 获取 000037 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:20,667 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:21,665 [WARNING] fetch_kline.py:255 Mootdx 获取 000042 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:21,666 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:22,804 [WARNING] fetch_kline.py:255 Mootdx 获取 000045 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:22,805 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:23,409 [WARNING] fetch_kline.py:255 Mootdx 获取 000048 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:23,410 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:23,468 [WARNING] fetch_kline.py:255 Mootdx 获取 000049 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:23,469 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:23,694 [WARNING] fetch_kline.py:255 Mootdx 获取 000050 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:23,766 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:24,274 [WARNING] fetch_kline.py:255 Mootdx 获取 000058 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:24,275 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:24,316 [WARNING] fetch_kline.py:255 Mootdx 获取 000059 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:24,317 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:26,991 [WARNING] fetch_kline.py:255 Mootdx 获取 000060 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:26,992 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:29,989 [WARNING] fetch_kline.py:255 Mootdx 获取 000061 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:29,990 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:29,993 [WARNING] fetch_kline.py:255 Mootdx 获取 000062 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:29,995 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:31,268 [WARNING] fetch_kline.py:255 Mootdx 获取 000063 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:31,269 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:31,868 [WARNING] fetch_kline.py:255 Mootdx 获取 000065 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:31,869 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:32,660 [WARNING] fetch_kline.py:255 Mootdx 获取 000066 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:32,661 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:32,675 [WARNING] fetch_kline.py:255 Mootdx 获取 000069 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:32,675 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:33,383 [WARNING] fetch_kline.py:255 Mootdx 获取 000070 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:33,384 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:34,059 [WARNING] fetch_kline.py:255 Mootdx 获取 000088 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:34,061 [WARNING] fetch_kline.py:255 Mootdx 获取 000078 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:34,062 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:34,063 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:35,942 [INFO] fetch_kline.py:492 用户中断，正在停止所有任务...
2025-07-25 01:23:36,464 [WARNING] fetch_kline.py:255 Mootdx 获取 000089 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:36,465 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:39,519 [WARNING] fetch_kline.py:255 Mootdx 获取 000090 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:39,520 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:39,523 [WARNING] fetch_kline.py:255 Mootdx 获取 000096 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:39,524 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:40,085 [WARNING] fetch_kline.py:255 Mootdx 获取 000099 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:40,085 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:41,245 [WARNING] fetch_kline.py:255 Mootdx 获取 000100 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:41,246 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:41,871 [WARNING] fetch_kline.py:255 Mootdx 获取 000156 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:41,871 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:41,878 [WARNING] fetch_kline.py:255 Mootdx 获取 000155 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:41,879 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:42,174 [WARNING] fetch_kline.py:255 Mootdx 获取 000157 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:42,175 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:23:43,740 [WARNING] fetch_kline.py:255 Mootdx 获取 000158 2025年数据失败: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.
2025-07-25 01:23:43,741 [WARNING] fetch_kline.py:256 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/works/StockTradebyZ/fetch_kline.py", line 249, in _get_kline_mootdx
    year_df = get_adjust_year(symbol=symbol, year=str(year), factor='01')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 336, in wrapped_f
    return copy(f, *args, **kw)
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 475, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 376, in iter
    result = action(retry_state)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/consts.py", line 118, in return_last_value
    return retry_state.outcome.result()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tenacity/__init__.py", line 478, in __call__
    result = fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/contrib/adjust.py", line 39, in get_adjust_year
    client = httpx.Client()
             ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 685, in __init__
    else self._init_proxy_transport(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_client.py", line 739, in _init_proxy_transport
    return HTTPTransport(
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/httpx/_transports/default.py", line 171, in __init__
    raise ImportError(
ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using `pip install httpx[socks]`.

2025-07-25 01:24:39,105 [INFO] fetch_kline.py:62 使用本地缓存的市值数据 (缓存时间: 2025-07-25)
2025-07-25 01:24:39,116 [INFO] fetch_kline.py:127 筛选得到 2123 只股票
2025-07-25 01:24:39,125 [INFO] fetch_kline.py:442 开始抓取 2123 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250725
2025-07-25 01:25:15,298 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:25:15,809 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:25:16,832 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:25:18,081 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:25:18,745 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:25:19,642 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:38,180 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:38,199 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:38,201 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:38,391 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:38,460 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:38,748 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:38,748 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:38,793 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:39,178 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:39,242 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:39,301 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:39,392 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:39,424 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:39,948 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:39,967 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:40,174 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:40,238 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:40,243 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:40,273 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:40,406 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:40,408 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:40,429 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:40,699 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:40,764 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:41,068 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:41,193 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:41,204 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:41,536 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:41,590 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:41,631 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:41,778 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:41,830 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:42,136 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:42,636 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:42,733 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:42,801 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 01:28:46,102 [INFO] fetch_kline.py:497 全部任务完成，数据已保存至 /Users/<USER>/works/StockTradebyZ/data
2025-07-25 10:08:42,877 [INFO] fetch_kline.py:62 使用本地缓存的市值数据 (缓存时间: 2025-07-25)
2025-07-25 10:08:42,885 [INFO] fetch_kline.py:127 筛选得到 2123 只股票
2025-07-25 10:08:42,886 [INFO] fetch_kline.py:442 开始抓取 2123 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250725
2025-07-25 10:10:42,427 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:12:53,019 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:12:55,942 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:12:56,778 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:12:58,405 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:12:58,604 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:12:58,655 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:12:58,974 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:00,396 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:00,424 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:00,605 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:00,679 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:00,718 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:00,755 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:00,939 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:01,052 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:01,071 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:01,169 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:01,431 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:02,743 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:03,144 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:03,396 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:04,363 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:05,020 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:05,047 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:05,217 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:05,712 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:05,890 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:06,872 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:06,905 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:07,033 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:07,051 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:07,433 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:07,550 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:08,562 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:08,844 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:08,902 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:08,941 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:09,183 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:09,269 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:09,697 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:10,599 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:10,789 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:11,219 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:11,419 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:12,683 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:12,811 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:12,984 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:14,457 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:14,486 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:14,679 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:14,831 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:14,852 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:15,077 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:15,895 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:15,960 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:16,228 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:16,465 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:16,618 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:16,754 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:17,397 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:17,654 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:17,684 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:18,333 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:18,448 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:18,884 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:19,122 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:20,075 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:20,094 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:20,748 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:22,177 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:22,363 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:23,917 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:23,926 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:23,954 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:23,990 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:24,023 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:24,603 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,213 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,360 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,624 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,640 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,671 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,684 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,717 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,730 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,733 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:25,938 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,051 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,492 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,583 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,603 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,615 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,617 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,623 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,647 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,840 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:27,928 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:29,191 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:29,438 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:29,613 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:29,621 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:30,467 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:13:31,266 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:21:16,952 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:21:17,377 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:21:18,459 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:21:19,165 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:21:21,306 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:21:23,471 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:21:48,900 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:24:54,492 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:24:57,016 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:25:29,219 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:25:30,506 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:26:20,675 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:26:25,046 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:27:55,555 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:27:57,033 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:27:59,784 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:02,698 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:02,778 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:04,568 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:04,876 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:06,365 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:06,782 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:08,176 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:08,462 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:08,552 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:08,734 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:08,858 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:08,930 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:08,970 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:09,058 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:09,175 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,246 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,540 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,595 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,617 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,634 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,640 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,663 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,664 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,686 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:17,692 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:47,188 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:47,220 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:48,599 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:48,604 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:48,638 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:49,593 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:50,129 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:50,447 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:50,638 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:51,245 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:51,878 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:52,210 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:52,493 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:53,876 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:54,125 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:56,347 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:57,691 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:57,751 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:58,727 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:58,728 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:28:59,229 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:00,779 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:01,991 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:09,193 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:10,787 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:10,949 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:11,480 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:12,164 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:12,344 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:12,344 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:13,325 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:13,419 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:13,957 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:16,069 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:31,779 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:29:53,907 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:30:11,627 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:30:27,261 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:30:38,121 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:30:44,941 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:30:57,662 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:06,000 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:18,980 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:19,991 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:21,979 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:23,810 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:26,357 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:26,664 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:26,697 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:26,845 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:27,925 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:28,096 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:28,542 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:30,267 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:30,532 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:32,204 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:33,612 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:34,742 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:35,233 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:36,316 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:36,765 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:36,799 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:38,604 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:38,730 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:39,303 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:39,891 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:40,642 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:41,631 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:41,890 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:41,962 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:44,056 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:45,383 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:45,571 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:45,581 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:45,701 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:45,759 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:45,972 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:47,502 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:47,590 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:47,939 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:49,586 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:49,734 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:49,934 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:51,696 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:52,100 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:53,580 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:53,639 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:53,999 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:55,794 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:57,367 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:57,894 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:31:59,675 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:03,599 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:05,236 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:06,600 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:07,067 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:07,918 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:08,364 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:09,710 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:09,978 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:11,162 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:11,281 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:11,460 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:12,840 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:12,987 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:13,115 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:13,335 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:14,694 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:15,206 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:16,144 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:16,465 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:16,570 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:17,945 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:18,332 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:20,493 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:20,738 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:21,502 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:22,473 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:22,559 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:22,583 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:22,953 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:22,970 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:23,032 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:24,693 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:24,837 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:26,337 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:26,420 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:28,061 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:29,652 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:29,706 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:30,124 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:30,347 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:31,461 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:31,914 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:32,041 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:32,327 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:33,326 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:33,955 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:34,068 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:34,943 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:35,850 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:36,098 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:36,339 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:37,255 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:37,463 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:37,582 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:40,687 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:42,698 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:43,033 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:43,118 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:43,352 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:45,159 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:45,530 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:45,613 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:47,376 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:47,406 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:47,742 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:47,892 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:48,062 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:49,041 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:49,548 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:49,636 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:49,645 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:49,676 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:49,696 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:49,730 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:49,930 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:50,690 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:50,939 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:51,270 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:51,388 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:52,222 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:52,901 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:53,814 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:56,442 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:56,503 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:57,327 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:57,496 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:58,904 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:32:59,045 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:03,543 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:04,520 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:04,970 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:06,370 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:06,726 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:08,278 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:08,448 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:09,156 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:09,846 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:10,012 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:10,815 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:12,574 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:14,187 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:15,403 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:16,317 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:16,921 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:17,869 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:18,831 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:19,409 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:20,691 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:20,885 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:25,885 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:26,994 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:27,713 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:27,979 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:28,103 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:28,211 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:28,368 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:28,467 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:30,253 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:30,895 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:30,902 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:32,358 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:32,863 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:32,975 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:33,202 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:33,305 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:34,858 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:35,398 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:36,438 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:37,149 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:38,689 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:43,713 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:33:47,577 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:28,438 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:34,101 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:36,222 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:36,395 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:36,408 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:38,381 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:38,436 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:38,519 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:42,699 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:46,429 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:50,619 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:52,742 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:55,111 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:58,164 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:58,266 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:35:59,612 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:00,036 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:00,201 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:01,797 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:02,152 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:02,395 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:04,091 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:09,080 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:10,447 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:11,609 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:11,708 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:12,342 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:12,358 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:12,704 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:14,623 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:15,521 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:16,101 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:17,791 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:17,921 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:17,955 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:18,966 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:19,547 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:19,899 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:19,991 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:20,020 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:21,701 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:21,736 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:21,772 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:21,782 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:21,799 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:21,873 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:22,087 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:22,347 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:23,088 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:23,512 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:23,653 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:23,691 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:23,800 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:23,814 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:23,910 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:24,007 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:24,069 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:25,492 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:25,575 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:25,590 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:25,674 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:25,699 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:25,808 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:25,907 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:26,212 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:26,458 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:26,557 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:26,811 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:26,919 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:27,265 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 10:36:27,990 [INFO] fetch_kline.py:497 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-25 16:52:15,839 [INFO] fetch_kline.py:62 使用本地缓存的市值数据 (缓存时间: 2025-07-25)
2025-07-25 16:52:15,848 [INFO] fetch_kline.py:127 筛选得到 2123 只股票
2025-07-25 16:52:15,851 [INFO] fetch_kline.py:442 开始抓取 2123 支股票 | 数据源:mootdx | 频率:day | 日期:20190101 → 20250725
2025-07-25 16:53:00,707 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 16:53:01,524 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 16:53:03,046 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 16:53:03,825 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 16:53:04,343 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 16:53:06,101 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 16:53:08,058 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 16:54:59,722 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 16:57:26,067 [INFO] fetch_kline.py:497 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-25 17:16:21,795 [INFO] fetch_kline.py:62 使用本地缓存的市值数据 (缓存时间: 2025-07-25)
2025-07-25 17:16:21,806 [INFO] fetch_kline.py:127 筛选得到 2123 只股票
2025-07-25 17:16:21,817 [INFO] fetch_kline.py:452 开始抓取 2123 支股票 | 数据源:mootdx | 频率:day | 日期:20190101 → 20250725
2025-07-25 17:17:07,994 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 17:17:08,383 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 17:17:10,392 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 17:17:11,584 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 17:17:12,887 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 17:17:14,970 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 17:17:15,320 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 17:19:51,389 [WARNING] adjust.py:70 请求失败，正重试...
2025-07-25 17:22:00,022 [INFO] fetch_kline.py:507 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-25 17:43:45,266 [INFO] fetch_kline.py:62 使用本地缓存的市值数据 (缓存时间: 2025-07-25)
2025-07-25 17:43:45,274 [INFO] fetch_kline.py:127 筛选得到 2123 只股票
2025-07-25 17:43:45,283 [INFO] fetch_kline.py:454 开始抓取 2123 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250725
2025-07-25 17:43:52,400 [INFO] fetch_kline.py:504 用户中断，正在停止所有任务...
2025-07-25 19:00:44,575 [INFO] fetch_kline.py:81 筛选得到 2112 只股票
2025-07-25 19:00:44,576 [INFO] fetch_kline.py:335 开始抓取 2112 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250725
2025-07-25 19:01:01,575 [ERROR] fetch_kline.py:283 000652 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-25 19:01:08,890 [ERROR] fetch_kline.py:283 002389 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-25 19:01:18,045 [WARNING] fetch_kline.py:190 Mootdx 拉取 689009 失败: sina qfq factor not available
2025-07-25 19:01:19,778 [ERROR] fetch_kline.py:283 600585 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-25 19:01:30,667 [ERROR] fetch_kline.py:283 603767 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-25 19:01:33,120 [INFO] fetch_kline.py:362 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-28 13:04:05,336 [INFO] fetch_kline.py:81 筛选得到 2114 只股票
2025-07-28 13:04:05,347 [INFO] fetch_kline.py:335 开始抓取 2125 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250728
2025-07-28 13:05:50,571 [ERROR] fetch_kline.py:283 002468 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-28 13:06:52,261 [WARNING] fetch_kline.py:190 Mootdx 拉取 600557 失败: timed out
2025-07-28 13:06:56,946 [WARNING] fetch_kline.py:190 Mootdx 拉取 600623 失败: timed out
2025-07-28 13:07:33,699 [WARNING] fetch_kline.py:190 Mootdx 拉取 601628 失败: timed out
2025-07-28 13:07:36,529 [WARNING] fetch_kline.py:190 Mootdx 拉取 601808 失败: timed out
2025-07-28 13:08:09,646 [WARNING] fetch_kline.py:190 Mootdx 拉取 689009 失败: sina qfq factor not available
2025-07-28 13:08:09,784 [INFO] fetch_kline.py:362 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-28 20:55:25,747 [INFO] fetch_kline.py:81 筛选得到 2117 只股票
2025-07-28 20:55:25,751 [INFO] fetch_kline.py:335 开始抓取 2126 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250728
2025-07-28 20:55:51,632 [ERROR] fetch_kline.py:283 002439 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-28 20:56:02,072 [WARNING] fetch_kline.py:190 Mootdx 拉取 689009 失败: sina qfq factor not available
2025-07-28 20:56:07,920 [ERROR] fetch_kline.py:283 601177 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-28 20:56:10,476 [INFO] fetch_kline.py:362 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-28 22:20:51,316 [INFO] fetch_kline.py:81 筛选得到 2117 只股票
2025-07-28 22:20:51,317 [INFO] fetch_kline.py:335 开始抓取 2117 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250728
2025-07-28 22:21:16,558 [ERROR] fetch_kline.py:283 002556 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-28 22:21:17,393 [ERROR] fetch_kline.py:283 002657 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-28 22:21:22,982 [WARNING] fetch_kline.py:190 Mootdx 拉取 689009 失败: sina qfq factor not available
2025-07-28 22:21:22,985 [INFO] fetch_kline.py:362 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-29 14:26:36,712 [INFO] fetch_missing_hs300_data.py:170 读取到 300 只股票代码
2025-07-29 14:26:36,715 [INFO] fetch_missing_hs300_data.py:181 发现 47 只股票缺少数据
2025-07-29 14:26:36,715 [INFO] fetch_missing_hs300_data.py:191 开始获取数据：数据源=akshare，频率=day，日期=20200101到20250729
2025-07-29 14:26:36,715 [INFO] fetch_missing_hs300_data.py:102 开始获取 47 只股票的数据，使用 3 个线程
2025-07-29 14:26:36,935 [INFO] fetch_missing_hs300_data.py:87 股票 688009 数据保存成功，共 1350 条记录
2025-07-29 14:26:36,935 [INFO] fetch_missing_hs300_data.py:87 股票 688012 数据保存成功，共 1350 条记录
2025-07-29 14:26:36,935 [INFO] fetch_missing_hs300_data.py:87 股票 688008 数据保存成功，共 1350 条记录
2025-07-29 14:26:37,114 [INFO] fetch_missing_hs300_data.py:87 股票 688041 数据保存成功，共 707 条记录
2025-07-29 14:26:37,132 [INFO] fetch_missing_hs300_data.py:87 股票 688047 数据保存成功，共 752 条记录
2025-07-29 14:26:37,136 [INFO] fetch_missing_hs300_data.py:87 股票 688036 数据保存成功，共 1350 条记录
2025-07-29 14:26:37,300 [INFO] fetch_missing_hs300_data.py:87 股票 688082 数据保存成功，共 896 条记录
2025-07-29 14:26:37,341 [INFO] fetch_missing_hs300_data.py:87 股票 688126 数据保存成功，共 1270 条记录
2025-07-29 14:26:37,342 [INFO] fetch_missing_hs300_data.py:87 股票 688111 数据保存成功，共 1350 条记录
2025-07-29 14:26:37,545 [INFO] fetch_missing_hs300_data.py:87 股票 688223 数据保存成功，共 848 条记录
2025-07-29 14:26:37,545 [INFO] fetch_missing_hs300_data.py:87 股票 688187 数据保存成功，共 941 条记录
2025-07-29 14:26:37,551 [INFO] fetch_missing_hs300_data.py:87 股票 688169 数据保存成功，共 1320 条记录
2025-07-29 14:26:37,741 [INFO] fetch_missing_hs300_data.py:87 股票 688303 数据保存成功，共 974 条记录
2025-07-29 14:26:37,742 [INFO] fetch_missing_hs300_data.py:87 股票 688271 数据保存成功，共 711 条记录
2025-07-29 14:26:37,764 [INFO] fetch_missing_hs300_data.py:87 股票 688256 数据保存成功，共 1220 条记录
2025-07-29 14:26:37,919 [INFO] fetch_missing_hs300_data.py:87 股票 688472 数据保存成功，共 518 条记录
2025-07-29 14:26:37,954 [INFO] fetch_missing_hs300_data.py:87 股票 688506 数据保存成功，共 619 条记录
2025-07-29 14:26:37,956 [INFO] fetch_missing_hs300_data.py:87 股票 688396 数据保存成功，共 1316 条记录
2025-07-29 14:26:38,117 [INFO] fetch_missing_hs300_data.py:87 股票 688599 数据保存成功，共 1246 条记录
2025-07-29 14:26:38,142 [INFO] fetch_missing_hs300_data.py:87 股票 688981 数据保存成功，共 1222 条记录
2025-07-29 14:26:38,160 [INFO] fetch_missing_hs300_data.py:87 股票 300059 数据保存成功，共 1350 条记录
2025-07-29 14:26:38,330 [INFO] fetch_missing_hs300_data.py:87 股票 300122 数据保存成功，共 1350 条记录
2025-07-29 14:26:38,346 [INFO] fetch_missing_hs300_data.py:87 股票 300124 数据保存成功，共 1350 条记录
2025-07-29 14:26:38,360 [INFO] fetch_missing_hs300_data.py:87 股票 300274 数据保存成功，共 1350 条记录
2025-07-29 14:26:38,561 [INFO] fetch_missing_hs300_data.py:87 股票 300316 数据保存成功，共 1350 条记录
2025-07-29 14:26:38,579 [INFO] fetch_missing_hs300_data.py:87 股票 300347 数据保存成功，共 1350 条记录
2025-07-29 14:26:38,594 [INFO] fetch_missing_hs300_data.py:87 股票 300308 数据保存成功，共 1350 条记录
2025-07-29 14:26:38,796 [INFO] fetch_missing_hs300_data.py:87 股票 300394 数据保存成功，共 1350 条记录
2025-07-29 14:26:38,824 [INFO] fetch_missing_hs300_data.py:87 股票 300408 数据保存成功，共 1350 条记录
2025-07-29 14:26:38,825 [INFO] fetch_missing_hs300_data.py:87 股票 300413 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,048 [INFO] fetch_missing_hs300_data.py:87 股票 300442 数据保存成功，共 1340 条记录
2025-07-29 14:26:39,053 [INFO] fetch_missing_hs300_data.py:87 股票 300433 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,054 [INFO] fetch_missing_hs300_data.py:87 股票 300418 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,281 [INFO] fetch_missing_hs300_data.py:87 股票 300502 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,291 [INFO] fetch_missing_hs300_data.py:87 股票 300628 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,291 [INFO] fetch_missing_hs300_data.py:87 股票 300498 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,527 [INFO] fetch_missing_hs300_data.py:87 股票 300750 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,529 [INFO] fetch_missing_hs300_data.py:87 股票 300661 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,530 [INFO] fetch_missing_hs300_data.py:87 股票 300759 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,752 [INFO] fetch_missing_hs300_data.py:87 股票 300832 数据保存成功，共 1267 条记录
2025-07-29 14:26:39,755 [INFO] fetch_missing_hs300_data.py:87 股票 300760 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,757 [INFO] fetch_missing_hs300_data.py:87 股票 300782 数据保存成功，共 1350 条记录
2025-07-29 14:26:39,952 [INFO] fetch_missing_hs300_data.py:87 股票 300999 数据保存成功，共 1163 条记录
2025-07-29 14:26:39,955 [INFO] fetch_missing_hs300_data.py:87 股票 300979 数据保存成功，共 1033 条记录
2025-07-29 14:26:39,959 [INFO] fetch_missing_hs300_data.py:87 股票 300896 数据保存成功，共 1170 条记录
2025-07-29 14:26:40,155 [INFO] fetch_missing_hs300_data.py:87 股票 301236 数据保存成功，共 819 条记录
2025-07-29 14:26:40,170 [INFO] fetch_missing_hs300_data.py:87 股票 301269 数据保存成功，共 717 条记录
2025-07-29 14:26:41,843 [INFO] fetch_missing_hs300_data.py:132 数据获取完成：成功 47 只，失败 0 只
2025-07-29 14:26:41,843 [INFO] fetch_missing_hs300_data.py:200 任务完成：成功获取 47/47 只股票的数据
2025-07-29 14:48:41,372 [INFO] fetch_kline.py:81 筛选得到 2113 只股票
2025-07-29 14:48:41,384 [INFO] fetch_kline.py:335 开始抓取 2176 支股票 | 数据源:mootdx | 频率:day | 日期:20190101 → 20250729
2025-07-29 14:50:04,415 [WARNING] fetch_kline.py:190 Mootdx 拉取 002287 失败: timed out
2025-07-29 14:52:51,964 [ERROR] fetch_kline.py:283 603299 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-29 14:52:52,907 [WARNING] fetch_kline.py:190 Mootdx 拉取 603767 失败: The read operation timed out
2025-07-29 14:52:52,988 [WARNING] fetch_kline.py:190 Mootdx 拉取 603855 失败: The read operation timed out
2025-07-29 14:52:53,460 [WARNING] fetch_kline.py:190 Mootdx 拉取 603325 失败: timed out
2025-07-29 14:53:07,366 [WARNING] fetch_kline.py:190 Mootdx 拉取 689009 失败: sina qfq factor not available
2025-07-29 14:53:07,368 [INFO] fetch_kline.py:362 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-30 12:15:30,184 [INFO] fetch_kline.py:81 筛选得到 2114 只股票
2025-07-30 12:15:30,188 [INFO] fetch_kline.py:335 开始抓取 2183 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250730
2025-07-30 12:15:56,228 [ERROR] fetch_kline.py:283 002467 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-30 12:16:06,968 [WARNING] fetch_kline.py:190 Mootdx 拉取 689009 失败: sina qfq factor not available
2025-07-30 12:16:16,112 [ERROR] fetch_kline.py:283 603108 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-30 12:16:18,646 [INFO] fetch_kline.py:362 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-30 12:17:20,342 [INFO] fetch_kline.py:81 筛选得到 2114 只股票
2025-07-30 12:17:20,350 [INFO] fetch_kline.py:335 开始抓取 2183 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250730
2025-07-30 12:17:39,326 [ERROR] fetch_kline.py:283 000882 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-30 12:17:57,837 [WARNING] fetch_kline.py:190 Mootdx 拉取 689009 失败: sina qfq factor not available
2025-07-30 12:18:09,447 [ERROR] fetch_kline.py:283 603605 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-30 12:18:11,719 [INFO] fetch_kline.py:362 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
2025-07-30 12:24:34,632 [INFO] fetch_kline.py:81 筛选得到 654 只股票
2025-07-30 12:24:34,633 [INFO] fetch_kline.py:335 开始抓取 654 支股票 | 数据源:mootdx | 频率:day | 日期:20200101 → 20250730
2025-07-30 12:24:44,460 [WARNING] fetch_kline.py:190 Mootdx 拉取 689009 失败: sina qfq factor not available
2025-07-30 12:24:53,556 [ERROR] fetch_kline.py:283 600060 第 1 次抓取失败
Traceback (most recent call last):
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 262, in fetch_one
    new_df = get_kline(code, start, end, "qfq", datasource, freq_code)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 220, in get_kline
    return _get_kline_mootdx(code, start, end, adjust, freq_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/works/stockTradebyz-ljf/fetch_kline.py", line 186, in _get_kline_mootdx
    client = Quotes.factory(market="std")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 44, in factory
    return StdQuotes(**kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/mootdx/quotes.py", line 157, in __init__
    self.client.connect(ip, int(port), time_out=timeout)
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/base_socket_client.py", line 194, in connect
    self.setup()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/hq.py", line 35, in setup
    SetupCmd1(self.client).call_api()
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 117, in call_api
    result = self._call_api()
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/stock/lib/python3.12/site-packages/tdxpy/parser/base.py", line 152, in _call_api
    head_buf = self.client.recv(self.rsp_header_len)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out
2025-07-30 12:24:54,798 [INFO] fetch_kline.py:362 全部任务完成，数据已保存至 /Users/<USER>/works/stockTradebyz-ljf/data
