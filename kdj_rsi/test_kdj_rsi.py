#!/usr/bin/env python3
"""
测试 KDJ & RSI 指标计算
验证 600066 股票在指定日期的 J 值是否符合预期
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from kdj_rsi_indicator import get_kdj_rsi_values, load_stock_data, calculate_kdj_rsi

def test_600066_j_values():
    """
    测试 600066 股票的 J 值
    预期结果：
    2025-06-04 J值为 15.76 
    2025-06-05 J 值为 5.77 
    2025-06-06 J 值为 17.32
    """
    print("测试 600066 股票的 KDJ & RSI 指标计算")
    print("=" * 60)
    
    stock_code = '600066'
    target_dates = ['2025-06-04', '2025-06-05', '2025-06-06']
    expected_j_values = [15.76, 5.77, 17.32]
    
    try:
        # 获取计算结果
        result = get_kdj_rsi_values(stock_code, target_dates)
        
        print("计算结果:")
        print("-" * 40)
        
        success_count = 0
        total_count = len(target_dates)
        
        for i, (_, row) in enumerate(result.iterrows()):
            date_str = row['date'].strftime('%Y-%m-%d')
            calculated_j = row['J']
            expected_j = expected_j_values[i]
            
            print(f"日期: {date_str}")
            print(f"KD: {row['KD']:.2f}")
            print(f"J: {calculated_j:.2f} (预期: {expected_j})")
            rsi_str = f"{row['RSI']:.2f}" if not np.isnan(row['RSI']) else 'N/A'
            print(f"RSI: {rsi_str}")
            
            # 检查 J 值是否在误差范围内（保留2位小数后比较）
            if abs(round(calculated_j, 2) - expected_j) < 0.01:
                print("✓ J 值验证通过")
                success_count += 1
            else:
                print(f"✗ J 值验证失败，差异: {abs(calculated_j - expected_j):.4f}")
            
            print("-" * 40)
        
        print(f"\n验证结果: {success_count}/{total_count} 通过")
        
        if success_count == total_count:
            print("🎉 所有测试通过！")
            return True
        else:
            print("❌ 部分测试失败，需要调整算法")
            return False
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_calculation_process():
    """
    调试计算过程，输出中间步骤
    """
    print("\n调试计算过程")
    print("=" * 60)
    
    stock_code = '600066'
    
    try:
        # 加载数据
        data = load_stock_data(stock_code)
        
        # 获取目标日期前后的数据用于调试
        target_date = pd.to_datetime('2025-06-04')
        
        # 找到目标日期的索引
        date_index = data[data['date'] == target_date].index
        if len(date_index) == 0:
            print(f"未找到日期 {target_date}")
            return
        
        idx = date_index[0]
        
        # 获取前后几天的数据用于调试
        start_idx = max(0, idx - 20)
        end_idx = min(len(data), idx + 5)
        
        debug_data = data.iloc[start_idx:end_idx].copy()
        
        print(f"调试数据范围: {debug_data['date'].iloc[0]} 到 {debug_data['date'].iloc[-1]}")
        print(f"目标日期 {target_date} 在索引 {idx - start_idx}")
        
        # 计算指标
        indicators = calculate_kdj_rsi(debug_data)
        
        # 输出目标日期前后的计算结果
        target_idx_in_debug = idx - start_idx
        
        print("\n目标日期前后的指标值:")
        for i in range(max(0, target_idx_in_debug - 2), min(len(indicators), target_idx_in_debug + 3)):
            if i < len(indicators):
                rsi_val = indicators['RSI'].iloc[i]
                rsi_str = f"{rsi_val:.4f}" if not np.isnan(rsi_val) else 'N/A'
                print(f"{debug_data['date'].iloc[i].strftime('%Y-%m-%d')}: "
                      f"KD={indicators['KD'].iloc[i]:.4f}, "
                      f"J={indicators['J'].iloc[i]:.4f}, "
                      f"RSI={rsi_str}")
        
    except Exception as e:
        print(f"调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行测试
    success = test_600066_j_values()
    
    # 如果测试失败，运行调试
    if not success:
        debug_calculation_process()
