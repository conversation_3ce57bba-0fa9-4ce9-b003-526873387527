#!/usr/bin/env python3
"""
KDJ & RSI 指标计算主程序

使用方法:
1. 计算指定股票的所有指标值:
   python main.py 600066

2. 计算指定股票在特定日期的指标值:
   python main.py 600066 --dates 2025-06-04 2025-06-05 2025-06-06

3. 保存结果到文件:
   python main.py 600066 --output results.csv

4. 显示帮助信息:
   python main.py --help
"""

import argparse
import sys
import os
import pandas as pd
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from kdj_rsi_indicator import get_kdj_rsi_values, load_stock_data, calculate_kdj_rsi

def main():
    parser = argparse.ArgumentParser(
        description='计算股票的 KDJ & RSI 指标',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s 600066                                    # 计算 600066 的所有指标
  %(prog)s 600066 --dates 2025-06-04 2025-06-05     # 计算特定日期的指标
  %(prog)s 600066 --output results.csv              # 保存结果到文件
  %(prog)s 600066 --latest 10                       # 显示最近 10 天的数据
        """
    )
    
    parser.add_argument('stock_code', help='股票代码，如 600066')
    parser.add_argument('--dates', nargs='+', help='指定日期列表，格式: YYYY-MM-DD')
    parser.add_argument('--output', '-o', help='输出文件路径 (CSV 格式)')
    parser.add_argument('--latest', '-l', type=int, help='显示最近 N 天的数据')
    parser.add_argument('--kdj-length', type=int, default=9, help='KDJ 计算周期 (默认: 9)')
    parser.add_argument('--kdj-signal', type=int, default=3, help='KDJ 信号平滑周期 (默认: 3)')
    parser.add_argument('--rsi-length', type=int, default=14, help='RSI 计算周期 (默认: 14)')
    
    args = parser.parse_args()
    
    try:
        print(f"正在计算股票 {args.stock_code} 的 KDJ & RSI 指标...")
        print(f"参数: KDJ周期={args.kdj_length}, KDJ信号={args.kdj_signal}, RSI周期={args.rsi_length}")
        print("=" * 60)
        
        # 加载数据
        data = load_stock_data(args.stock_code)
        
        # 计算指标
        indicators = calculate_kdj_rsi(
            data, 
            kdj_length=args.kdj_length,
            kdj_signal=args.kdj_signal,
            rsi_length=args.rsi_length
        )
        
        # 根据参数筛选数据
        if args.dates:
            # 指定日期
            target_dates = pd.to_datetime(args.dates)
            result = indicators[indicators['date'].isin(target_dates)]
            if len(result) == 0:
                print("警告: 未找到指定日期的数据")
                return
        elif args.latest:
            # 最近 N 天
            result = indicators.tail(args.latest)
        else:
            # 所有数据
            result = indicators
        
        # 显示结果
        print("计算结果:")
        print("-" * 60)
        
        for _, row in result.iterrows():
            date_str = row['date'].strftime('%Y-%m-%d')
            kd_val = row['KD']
            j_val = row['J']
            rsi_val = row['RSI']
            
            print(f"日期: {date_str}")
            print(f"  KD:  {kd_val:.2f}")
            print(f"  J:   {j_val:.2f}")
            
            if not np.isnan(rsi_val):
                print(f"  RSI: {rsi_val:.2f}")
            else:
                print(f"  RSI: N/A")
            print()
        
        # 保存到文件
        if args.output:
            result.to_csv(args.output, index=False, float_format='%.2f')
            print(f"结果已保存到: {args.output}")
        
        print(f"共计算了 {len(result)} 条记录")
        
    except FileNotFoundError as e:
        print(f"错误: 数据文件不存在 - {e}")
        print(f"请确保 ../data/{args.stock_code}.csv 文件存在")
    except Exception as e:
        print(f"计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
