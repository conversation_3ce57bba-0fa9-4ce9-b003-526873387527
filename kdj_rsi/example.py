#!/usr/bin/env python3
"""
KDJ & RSI 指标计算示例

这个文件展示了如何在 Python 代码中直接使用 KDJ & RSI 指标计算功能
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from kdj_rsi_indicator import get_kdj_rsi_values, load_stock_data, calculate_kdj_rsi

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    stock_code = '600066'
    target_dates = ['2025-06-04', '2025-06-05', '2025-06-06']
    
    # 获取指定日期的指标值
    result = get_kdj_rsi_values(stock_code, target_dates)
    
    print(f"股票 {stock_code} 在指定日期的 KDJ & RSI 指标:")
    for _, row in result.iterrows():
        date_str = row['date'].strftime('%Y-%m-%d')
        print(f"{date_str}: KD={row['KD']:.2f}, J={row['J']:.2f}, RSI={row['RSI']:.2f}")
    
    return result

def example_custom_parameters():
    """自定义参数示例"""
    print("\n=== 自定义参数示例 ===")
    
    stock_code = '600066'
    
    # 加载数据
    data = load_stock_data(stock_code)
    
    # 使用自定义参数计算指标
    indicators = calculate_kdj_rsi(
        data, 
        kdj_length=14,    # 使用 14 天周期
        kdj_signal=5,     # 使用 5 天信号平滑
        rsi_length=21     # 使用 21 天 RSI
    )
    
    # 获取最近 5 天的数据
    recent_data = indicators.tail(5)
    
    print("使用自定义参数 (KDJ周期=14, 信号=5, RSI周期=21) 的最近 5 天数据:")
    for _, row in recent_data.iterrows():
        date_str = row['date'].strftime('%Y-%m-%d')
        rsi_str = f"{row['RSI']:.2f}" if not np.isnan(row['RSI']) else 'N/A'
        print(f"{date_str}: KD={row['KD']:.2f}, J={row['J']:.2f}, RSI={rsi_str}")
    
    return recent_data

def example_data_analysis():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    stock_code = '600066'
    
    # 获取所有数据
    data = load_stock_data(stock_code)
    indicators = calculate_kdj_rsi(data)
    
    # 统计分析
    j_values = indicators['J'].dropna()
    rsi_values = indicators['RSI'].dropna()
    
    print(f"股票 {stock_code} 的指标统计:")
    print(f"J 值统计:")
    print(f"  平均值: {j_values.mean():.2f}")
    print(f"  最大值: {j_values.max():.2f}")
    print(f"  最小值: {j_values.min():.2f}")
    print(f"  标准差: {j_values.std():.2f}")
    
    print(f"RSI 值统计:")
    print(f"  平均值: {rsi_values.mean():.2f}")
    print(f"  最大值: {rsi_values.max():.2f}")
    print(f"  最小值: {rsi_values.min():.2f}")
    print(f"  标准差: {rsi_values.std():.2f}")
    
    # 找出 J 值的极值点
    j_max_date = indicators.loc[indicators['J'].idxmax(), 'date']
    j_min_date = indicators.loc[indicators['J'].idxmin(), 'date']
    j_max_value = indicators['J'].max()
    j_min_value = indicators['J'].min()
    
    print(f"\nJ 值极值:")
    print(f"  最高点: {j_max_date.strftime('%Y-%m-%d')} = {j_max_value:.2f}")
    print(f"  最低点: {j_min_date.strftime('%Y-%m-%d')} = {j_min_value:.2f}")
    
    return indicators

def example_save_to_file():
    """保存到文件示例"""
    print("\n=== 保存到文件示例 ===")
    
    stock_code = '600066'
    
    # 获取最近 30 天的数据
    data = load_stock_data(stock_code)
    indicators = calculate_kdj_rsi(data)
    recent_data = indicators.tail(30)
    
    # 保存到 CSV 文件
    output_file = f'{stock_code}_kdj_rsi_recent.csv'
    recent_data.to_csv(output_file, index=False, float_format='%.2f')
    
    print(f"已将最近 30 天的数据保存到: {output_file}")
    print(f"文件包含 {len(recent_data)} 条记录")
    
    # 显示文件的前几行
    print("\n文件内容预览:")
    print(recent_data.head().to_string(index=False, float_format='%.2f'))
    
    return output_file

if __name__ == "__main__":
    try:
        # 运行所有示例
        example_basic_usage()
        example_custom_parameters()
        example_data_analysis()
        example_save_to_file()
        
        print("\n🎉 所有示例运行完成！")
        
    except Exception as e:
        print(f"运行示例时出现错误: {e}")
        import traceback
        traceback.print_exc()
