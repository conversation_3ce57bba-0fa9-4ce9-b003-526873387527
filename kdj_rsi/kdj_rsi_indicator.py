import talib as ta
import numpy as np
import pandas as pd
import os

def rma(series, length):
    """
    实现 Pine Script 中的 rma (Running Moving Average) 函数
    相当于指数移动平均 EMA，但使用不同的 alpha 值
    rma(x, length) = alpha * x + (1 - alpha) * rma[1]
    其中 alpha = 1 / length
    """
    alpha = 1.0 / length
    result = np.zeros_like(series)
    result[0] = series[0]
    
    for i in range(1, len(series)):
        if not np.isnan(series[i]):
            result[i] = alpha * series[i] + (1 - alpha) * result[i-1]
        else:
            result[i] = result[i-1]
    
    return result

def calculate_kdj_rsi(data, kdj_length=9, kdj_signal=3, rsi_length=14):
    """
    计算 KDJ 和 RSI 指标
    
    参数:
    data: DataFrame，包含 high, low, close 列
    kdj_length: KDJ 计算周期，默认 9
    kdj_signal: KDJ 信号平滑周期，默认 3
    rsi_length: RSI 计算周期，默认 14
    
    返回:
    DataFrame，包含 KD, J, RSI 列
    """
    high = data['high'].values
    low = data['low'].values
    close = data['close'].values
    
    # 计算 KDJ
    # hi = highest(high, length)
    # lo = lowest(low, length)
    hi = ta.MAX(high, kdj_length)
    lo = ta.MIN(low, kdj_length)
    
    # k = 100*((close-lo)/(hi-lo))
    k = 100 * ((close - lo) / (hi - lo))
    k = np.where(np.isnan(k), 0, k)  # 处理除零情况
    
    # pK = rma(k, signal)
    # pD = rma(pK, signal)
    pK = rma(k, kdj_signal)
    pD = rma(pK, kdj_signal)
    
    # pJ = 3*pK-2*pD
    pJ = 3 * pK - 2 * pD
    
    # KD = avg(pK, pD)
    KD = (pK + pD) / 2
    
    # 计算 RSI
    # rsi = (rsi(close, rsi_length)-35)*2
    rsi_raw = ta.RSI(close, rsi_length)
    rsi = (rsi_raw - 35) * 2
    
    # 创建结果 DataFrame
    result = pd.DataFrame({
        'date': data['date'],
        'KD': KD,
        'J': pJ,
        'RSI': rsi
    })
    
    return result

def load_stock_data(stock_code):
    """
    加载股票数据
    
    参数:
    stock_code: 股票代码，如 '600066'
    
    返回:
    DataFrame，包含股票数据
    """
    data_path = os.path.join('..', 'data', f'{stock_code}.csv')
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    data = pd.read_csv(data_path)
    data['date'] = pd.to_datetime(data['date'])
    return data

def get_kdj_rsi_values(stock_code, target_dates=None):
    """
    获取指定股票的 KDJ 和 RSI 值
    
    参数:
    stock_code: 股票代码
    target_dates: 目标日期列表，如果为 None 则返回所有日期
    
    返回:
    DataFrame，包含指定日期的指标值
    """
    # 加载数据
    data = load_stock_data(stock_code)
    
    # 计算指标
    indicators = calculate_kdj_rsi(data)
    
    # 如果指定了目标日期，则筛选
    if target_dates:
        target_dates = pd.to_datetime(target_dates)
        indicators = indicators[indicators['date'].isin(target_dates)]
    
    return indicators

if __name__ == "__main__":
    # 测试代码
    stock_code = '600066'
    target_dates = ['2025-06-04', '2025-06-05', '2025-06-06']
    
    try:
        result = get_kdj_rsi_values(stock_code, target_dates)
        print("KDJ & RSI 指标计算结果:")
        print("=" * 50)
        for _, row in result.iterrows():
            print(f"日期: {row['date'].strftime('%Y-%m-%d')}")
            print(f"KD: {row['KD']:.2f}")
            print(f"J: {row['J']:.2f}")
            print(f"RSI: {row['RSI']:.2f if not np.isnan(row['RSI']) else 'N/A'}")
            print("-" * 30)
            
    except Exception as e:
        print(f"错误: {e}")
