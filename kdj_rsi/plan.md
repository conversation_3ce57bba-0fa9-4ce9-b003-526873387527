## hdly 的 pine 代码
```pine

study("KDJ & RSI")

length = input(9, title="period")
signal = input(3, title="signal")
hi = highest(high, length)
lo = lowest(low , length)
k = 100*((close-lo)/(hi-lo))
pK = rma(k, signal)
pD = rma(pK, signal)
pJ = 3*pK-2*pD
plot(avg(pK, pD), color=orange, transp=0, linewidth=1, title="KD")
plot(pJ, color=fuchsia, transp=0, linewidth=1, title="J")

rsi_length = input(14, title="RSI")
rsi = (rsi(close, rsi_length)-35)*2
rsi_color = rsi >= 70 or rsi <= -10 ? white : color(white, 80)
plot(rsi, color=rsi_color, transp=0, linewidth=2, style=circles, title="RSI")

```
## 任务
以上代码是 pinescript 编写的，将其转化为 python 来执行，其中指标的计算可以用 talib 库来计算

## 预期结果以及检验
选取 600066 做例子：
2025-06-04 J值为 15.76 
2025-06-05 J 值为 5.77 
2025-06-06 J 值为 17.32

### 注意
J 值可能有多位小数，保留 2 位小数后再对比校验
RSI 值可能为空

## 规范
1. 所有代码在 kdj_rsi 文件夹内实现
2. 数据可以从根目录的 data 文件夹内读取，数据文件为 [股票代码].csv
