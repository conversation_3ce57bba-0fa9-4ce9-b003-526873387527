# KDJ & RSI 指标计算器

这个模块实现了将 Pine Script 的 KDJ & RSI 指标计算转换为 Python 版本，使用 talib 库进行技术指标计算。

## 功能特性

- **KDJ 指标计算**: 实现了完整的 KDJ 指标计算，包括 K、D、J 值
- **RSI 指标计算**: 实现了 RSI 指标计算，并按照 Pine Script 公式进行调整
- **精确匹配**: 计算结果与原始 Pine Script 代码完全一致
- **灵活参数**: 支持自定义 KDJ 和 RSI 的计算参数
- **多种输出**: 支持控制台显示和 CSV 文件导出

## 文件说明

- `kdj_rsi_indicator.py`: 核心指标计算模块
- `main.py`: 主程序，提供命令行接口
- `test_kdj_rsi.py`: 测试文件，验证计算结果的准确性
- `plan.md`: 原始需求文档

## 安装依赖

确保已安装以下 Python 包：

```bash
conda activate stock
pip install talib pandas numpy
```

## 使用方法

### 1. 基本使用

计算指定股票的所有 KDJ & RSI 指标：

```bash
python main.py 600066
```

### 2. 计算特定日期

计算指定日期的指标值：

```bash
python main.py 600066 --dates 2025-06-04 2025-06-05 2025-06-06
```

### 3. 显示最近数据

显示最近 N 天的指标数据：

```bash
python main.py 600066 --latest 10
```

### 4. 保存到文件

将计算结果保存为 CSV 文件：

```bash
python main.py 600066 --output results.csv
```

### 5. 自定义参数

使用自定义的计算参数：

```bash
python main.py 600066 --kdj-length 14 --kdj-signal 5 --rsi-length 21
```

### 6. 查看帮助

```bash
python main.py --help
```

## 算法说明

### KDJ 指标

KDJ 指标基于随机指标 KD，计算公式如下：

1. **RSV (Raw Stochastic Value)**:
   ```
   RSV = (收盘价 - N日内最低价) / (N日内最高价 - N日内最低价) × 100
   ```

2. **K 值**: RSV 的移动平均
   ```
   K = RMA(RSV, signal_period)
   ```

3. **D 值**: K 值的移动平均
   ```
   D = RMA(K, signal_period)
   ```

4. **J 值**: 
   ```
   J = 3 × K - 2 × D
   ```

5. **KD 值**: K 和 D 的平均值
   ```
   KD = (K + D) / 2
   ```

### RSI 指标

RSI 指标经过调整以匹配 Pine Script 的计算：

```
RSI = (标准RSI - 35) × 2
```

其中标准 RSI 使用 talib.RSI() 计算。

### RMA 函数

实现了 Pine Script 中的 RMA (Running Moving Average) 函数：

```
RMA(x, length) = alpha × x + (1 - alpha) × RMA[1]
```

其中 `alpha = 1 / length`

## 验证结果

使用 600066 股票数据进行验证，结果完全匹配预期值：

| 日期 | J 值 | 预期值 | 状态 |
|------|------|--------|------|
| 2025-06-04 | 15.76 | 15.76 | ✓ |
| 2025-06-05 | 5.77 | 5.77 | ✓ |
| 2025-06-06 | 17.32 | 17.32 | ✓ |

## 运行测试

验证计算结果的准确性：

```bash
python test_kdj_rsi.py
```

## 注意事项

1. **数据格式**: 股票数据文件应位于 `../data/` 目录下，文件名格式为 `{股票代码}.csv`
2. **数据列**: CSV 文件应包含 `date`, `high`, `low`, `close` 列
3. **精度**: J 值保留 2 位小数进行比较验证
4. **RSI 空值**: 在数据不足时，RSI 值可能为空 (NaN)

## 示例输出

```
正在计算股票 600066 的 KDJ & RSI 指标...
参数: KDJ周期=9, KDJ信号=3, RSI周期=14
============================================================
计算结果:
------------------------------------------------------------
日期: 2025-06-04
  KD:  28.59
  J:   15.76
  RSI: 11.15

日期: 2025-06-05
  KD:  23.91
  J:   5.77
  RSI: 9.54

日期: 2025-06-06
  KD:  24.62
  J:   17.32
  RSI: 15.27

共计算了 3 条记录
```
