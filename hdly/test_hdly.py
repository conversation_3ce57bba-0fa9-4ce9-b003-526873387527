#!/usr/bin/env python3
"""
HDLY 指标测试脚本
简化版本，专注于核心功能测试
"""

import pandas as pd
from hdly_indicator import get_hdly_value


def load_stock_data(stock_code):
    """加载股票数据"""
    try:
        file_path = f"../data/{stock_code}.csv"
        data = pd.read_csv(file_path)
        data['date'] = pd.to_datetime(data['date'])
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None


def test_hdly_600066():
    """测试 600066 股票的 HDLY 指标计算"""
    print("HDLY 指标测试 - 股票代码: 600066")
    print("=" * 60)

    try:
        # 加载数据
        data = load_stock_data("600066")
        if data is None:
            return None

        # 计算指标（保持原始数据顺序：从过去到现在）
        big_money_values = get_hdly_value(data['low'])
        print("BIG_MONEY values:", big_money_values.tail(60))
        print("data values:", data['low'].tail(60))
        # 创建结果 DataFrame
        result = data.copy()
        result['BIG_MONEY'] = big_money_values
        result['BIG_MONEY_PLOT'] = big_money_values * 2

        # 目标日期和预期值
        test_cases = [
            ("2025-06-04", 23.44),
            ("2025-06-05", 37.25),
            ("2025-06-06", 18.63)
        ]

        print("计算结果对比:")
        print("-" * 60)
        print(f"{'日期':<12} {'计算值':<10} {'预期值':<10} {'差异':<10} {'状态'}")
        print("-" * 60)

        all_passed = True

        for date_str, expected in test_cases:
            target_date = pd.to_datetime(date_str)
            row = result[result['date'] == target_date]
            if not row.empty:
                actual = row['BIG_MONEY_PLOT'].iloc[0]
                diff = abs(actual - expected)
                tolerance = expected * 0.2  # 20% 容差
                status = "✓ 通过" if diff <= tolerance else "✗ 失败"

                if diff > tolerance:
                    all_passed = False

                print(f"{date_str:<12} {actual:<10.2f} {expected:<10.2f} {diff:<10.2f} {status}")
            else:
                print(f"{date_str:<12} {'无数据':<10} {expected:<10.2f} {'N/A':<10} {'✗ 失败'}")
                all_passed = False

        print("-" * 60)
        print(f"总体结果: {'✓ 所有测试通过' if all_passed else '✗ 部分测试失败'}")

        if not all_passed:
            print("\n注意: 计算结果与预期值有差异，可能的原因:")
            print("1. Pine Script 与 Python 在数值计算上的细微差异")
            print("2. EMA 或 SMA 算法实现的差异")
            print("3. 数据精度或舍入方式的不同")
            print("4. Pine Script 中可能有未明确的默认参数")

        return result

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return None


def show_calculation_details(result, date_str):
    """显示指定日期的详细计算过程"""
    if result is None:
        print("无计算结果")
        return

    target_date = pd.to_datetime(date_str)
    row = result[result['date'] == target_date]
    if row.empty:
        print(f"未找到 {date_str} 的数据")
        return

    r = row.iloc[0]
    print(f"\n{date_str} 详细计算过程:")
    print("-" * 40)
    print(f"输入数据:")
    print(f"  low: {r['low']:.4f}")
    print(f"\n最终结果:")
    print(f"  BIG_MONEY: {r['BIG_MONEY']:.4f}")
    print(f"  BIG_MONEY*2: {r['BIG_MONEY_PLOT']:.4f}")


if __name__ == "__main__":
    # 运行测试
    result = test_hdly_600066()

    # 显示详细计算过程
    if result is not None:
        print("\n" + "=" * 60)
        show_calculation_details(result, "2025-06-04")
