//@version=5
indicator("HDLYU3")
//富途版SMA
SMA(s, l, m) =>
    _bcwsma = float(na)
    _s = s
    _l = l
    _m = m
    _bcwsma := (_m * _s + (_l - _m) * nz(_bcwsma[1])) / _l
    _bcwsma

// N=5
// high_PART=(high-low)/(high-low)
// BODY_PART=(low-open)/(high-low)
// low_PART=(open-low)/(high-low)
// PULL_M=BODY_PART*0.8*volume
// PULL_UP=high_PART*0.2*volume
// PULL_DOWN=low_PART*0.2*volume
// WASH_M=BODY_PART*0.2*volume
// WASH_UP=high_PART*0.8*volume
// WASH_DOWN=low_PART*0.8*volume
// Z1=PULL_M+PULL_UP+PULL_DOWN
// F1=WASH_M+WASH_UP+WASH_DOWN
// INCREMENTAL=(Z1-F1)
// VA1=if low>low[1]
//     volume
// else
//     INCREMENTAL
// A1=(math.sum(VA1,N)-ta.lowest(VA1,N))/10000
// K1=SMA(A1,2,1)
// VARB1=low[1]
// VARC1=SMA(math.abs(low-VARB1),3,1)/SMA(math.max(low-VARB1,0),3,1)*100
// VARD1=ta.ema(VARC1*10,3)
// VARE1=ta.lowest(low,30)
// VARF1=ta.highest(VARD1,30)
// VALUE_ASSESSMENT=ta.highest(K1,200)/100
// K2=SMA(A1,3,1)/VALUE_ASSESSMENT
// D2=SMA(K2,3,1)
// J2=3*K2-2*D2

// STOP=ta.lowest(low,15)
// PRICE=ta.ema(low,2)

// RETAIL1=(ta.highest(VA1,24)-math.sum(VA1,24))/10000/VALUE_ASSESSMENT
// MM1=J2

VARB=low[1]
VARC1 = SMA(math.abs(low-VARB),3,1)
VARC2 = SMA(math.max(low-VARB,0),3,1)
VARC=VARC1/VARC2*100
log.info('{0}',VARC)
VARD=ta.ema(VARC*10,3)
VARE=ta.lowest(low,30)
VARF=ta.highest(VARD,30)

BIG_MONEY_COND = if low<=VARE
    (VARD+VARF*2)/2
else 
    0

BIG_MONEY=ta.ema(BIG_MONEY_COND,3)/618
BIG_MONEY1=if BIG_MONEY>100
    100
else
    BIG_MONEY

// BIG_MONEY1 := BIG_MONEY

// plot(VARF, 'VARF')

plotcandle(0, BIG_MONEY*2, 0, BIG_MONEY*2, title='BIG_MONEY1',color = (BIG_MONEY1>-150) and (BIG_MONEY1>=BIG_MONEY1[1]) ? color.rgb(211,109,117,0) : color.rgb(129,194,77,0))
// plot(BIG_MONEY, 'BIG_MONEY_COND')
