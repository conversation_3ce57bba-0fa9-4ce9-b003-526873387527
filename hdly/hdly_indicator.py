import talib as ta
import numpy as np
import pandas as pd

def SMA(s, l, m, name):
  _bcwsma = []
  for index in range(len(s)):
    value = m * s[index]
    if np.isnan(value) == False:
      _bcwsma.append((value + (l - m) * _bcwsma[index - 1]) / l)
    else:
      _bcwsma.append(0)
  df = pd.DataFrame({f"{name}": _bcwsma})
  df['datetime'] = s.index
  df.set_index('datetime', inplace=True)
  return df
def get_hdly_value(low):
  VARB=low.shift(1)
  VARC1 = SMA(abs(low-VARB), 3, 1, "VARC1")
  VARB_MAX = low-VARB
  for index in range(len(VARB_MAX)):
    if (VARB_MAX[index] < 0):
      VARB_MAX[index] = 0
  VARC2 = SMA(VARB_MAX, 3, 1, "VARC2")
  
  VARC=(VARC1['VARC1']/VARC2['VARC2']*100).replace([np.inf, -np.inf], np.nan)
  VARD=ta.EMA(VARC*10, 3)
  VARE=ta.MIN(low,30)
  VARF=ta.MAX(VARD,30)
  BIG_MONEY_COND = ((VARD+VARF*2)/2).fillna(0)
  
  for index in range(len(low)):
    if low[index]>VARE[index]:
      BIG_MONEY_COND[index] = 0
  BIG_MONEY=(ta.EMA(BIG_MONEY_COND.replace([np.inf, -np.inf], np.nan), 3)/618)
  # BIG_MONEY_COND.to_csv('./a.txt')
  # print(BIG_MONEY_COND)
  return BIG_MONEY