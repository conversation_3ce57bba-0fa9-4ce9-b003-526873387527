# 基于官方Python镜像
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 复制依赖和源码
COPY requirements.txt ./
COPY fetch_kline.py ./
COPY select_stock.py ./
COPY Selector.py ./
COPY buy_in.py ./
COPY reversion.py ./
COPY docker_entrypoint.sh ./
COPY configs.json ./
COPY appendix.json ./
COPY email_config.json ./
COPY email_template.html ./
COPY stock_names.csv ./
COPY auto_run.py ./

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 设置启动脚本权限
RUN chmod +x /app/docker_entrypoint.sh

# 使用启动脚本作为入口点
ENTRYPOINT ["/app/docker_entrypoint.sh"]
CMD ["python", "auto_run.py"]

# 可选：为常用脚本提供快捷方式
# 例如：docker run ... python fetch_kline.py --help
# 或   docker run ... python select_stock.py --help 