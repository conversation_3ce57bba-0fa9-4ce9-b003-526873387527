import subprocess
import time
import datetime
import json
import smtplib
from email.mime.text import MIMEText
from email.header import Header
from pathlib import Path
import os
import pytz

# 配置
FETCH_CMD = ["python", "fetch_kline.py", "--datasource", "mootdx", "--frequency", "4", "--out", "./data", "--workers", "10"]
SELECT_CMD = ["python", "select_stock.py", "--data-dir", "./data", "--config", "./configs.json"]
LOG_FILE = "select_results.log"
EMAIL_CONFIG_FILE = "email_config.json"

# 时区配置 - 使用中国时区
CHINA_TZ = pytz.timezone('Asia/Shanghai')

def get_china_time():
    """获取中国时间"""
    return datetime.datetime.now(CHINA_TZ)

# 邮件发送函数
def send_email(subject, content, config_path=EMAIL_CONFIG_FILE):
    with open(config_path, "r", encoding="utf-8") as f:
        cfg = json.load(f)
    host = cfg["host"]
    port = cfg["port"]
    user = cfg["auth"]["user"]
    password = cfg["auth"]["pass"]
    receivers = [user]  # 默认发给自己，可扩展

    msg = MIMEText(content, "plain", "utf-8")
    msg["From"] = user
    msg["To"] = ",".join(receivers)
    msg["Subject"] = Header(subject, "utf-8")

    with smtplib.SMTP_SSL(host, port) as server:
        server.login(user, password)
        server.sendmail(user, receivers, msg.as_string())

# 获取选股结果内容
def get_select_result(log_path=LOG_FILE):
    if not Path(log_path).exists():
        return "选股结果日志不存在。"
    with open(log_path, "r", encoding="utf-8") as f:
        return f.read()[-5000:]  # 只取最后5k字符，防止过大

# 执行主流程
def run_once():
    china_time = get_china_time()
    print(f"[{china_time}] 开始执行选股流程...")
    try:
        # 0. mootdx测速
        print("正在进行mootdx测速...")
        subprocess.run(["python", "-m", "mootdx", "bestip", "-vv"], check=True)

        # 1. 抓取/更新数据
        print("正在抓取/更新股票数据...")
        subprocess.run(FETCH_CMD, check=True)

        # 2. 选股
        print("正在执行选股...")
        subprocess.run(SELECT_CMD, check=True)

        # 3. 读取结果
        result = get_select_result()
        print("选股流程执行完成")

        # 4. 发送邮件功能暂时关闭
        # today = china_time.strftime('%Y-%m-%d')
        # try:
        #     send_email(f"选股结果 {today}", result)
        #     print(f"[{get_china_time()}] 邮件发送成功")
        # except Exception as email_error:
        #     print(f"[{get_china_time()}] 邮件发送失败: {email_error}")

    except Exception as e:
        print(f"[{get_china_time()}] 执行失败: {e}")
        raise

# 检查是否已在指定时间执行过
def should_run_at_time(hour, minute=0):
    """检查是否应该在指定时间执行，避免重复执行"""
    china_time = get_china_time()
    print(f"[{china_time}] 检查执行时间: 当前时间 {china_time.hour:02d}:{china_time.minute:02d}, 目标时间 {hour:02d}:{minute:02d}")

    if china_time.hour != hour or china_time.minute != minute:
        return False

    # 使用文件锁机制避免重复执行
    lock_file = f"/tmp/auto_run_lock_{hour}_{minute}_{china_time.strftime('%Y%m%d')}.lock"

    # 原子性检查和创建锁文件
    try:
        # 使用 O_CREAT | O_EXCL 标志确保原子性创建
        import fcntl
        fd = os.open(lock_file, os.O_CREAT | os.O_EXCL | os.O_WRONLY, 0o644)
        try:
            os.write(fd, str(china_time).encode())
        finally:
            os.close(fd)
        print(f"[{china_time}] 创建锁文件成功: {lock_file}")
        return True
    except FileExistsError:
        # 文件已存在，说明已经执行过
        print(f"[{china_time}] 锁文件已存在，跳过执行: {lock_file}")
        return False
    except Exception as e:
        print(f"[{china_time}] 创建锁文件失败: {e}")
        return False

# 定时调度主循环
def main():
    china_time = get_china_time()
    print(f"[{china_time}] 自动选股程序启动 (中国时间)")

    # 首次启动标志文件
    startup_flag = "/tmp/auto_run_startup.flag"
    first_run = not os.path.exists(startup_flag)

    if first_run:
        print("首次启动，立即执行一次选股...")
        try:
            run_once()
            # 创建启动标志文件
            with open(startup_flag, 'w') as f:
                f.write(str(get_china_time()))
        except Exception as e:
            print(f"首次执行失败: {e}")

    print("进入定时调度循环...")
    print("定时执行时间: 16:00 (收盘后) 和 09:10 (开盘前)")

    while True:
        try:
            # 16:00执行 (收盘后)
            if should_run_at_time(16, 0):
                china_time = get_china_time()
                print(f"[{china_time}] 16:00 定时执行 (收盘后选股)")
                run_once()
                time.sleep(65)  # 执行后休眠65秒，避免重复触发
                continue

            # 09:10执行 (开盘前)
            if should_run_at_time(9, 10):
                china_time = get_china_time()
                print(f"[{china_time}] 09:10 定时执行 (开盘前选股)")
                run_once()
                time.sleep(65)  # 执行后休眠65秒，避免重复触发
                continue

            # 正常情况下每分钟检查一次
            time.sleep(60)

        except KeyboardInterrupt:
            china_time = get_china_time()
            print(f"[{china_time}] 程序被用户中断")
            break
        except Exception as e:
            china_time = get_china_time()
            print(f"[{china_time}] 程序异常: {e}")
            time.sleep(60)  # 异常后等待60秒再继续

if __name__ == "__main__":
    main() 