import os
import pandas as pd
from mootdx.quotes import Quotes
from mootdx import consts
from mootdx.utils import get_stock_market

# 全局缓存股票名称
_stock_names_cache = {}

def get_stock_name(stock_code, client=None):
    """获取股票名称，优先从CSV文件读取，如果没有则更新整个市场股票列表"""
    # 先检查内存缓存
    if stock_code in _stock_names_cache:
        return _stock_names_cache[stock_code]

    # 尝试从CSV文件读取
    stock_name = load_stock_name_from_csv(stock_code)
    if stock_name:
        _stock_names_cache[stock_code] = stock_name
        return stock_name

    # 如果CSV中没有，更新整个市场的股票列表
    print(f"股票 {stock_code} 不在缓存中，正在更新股票列表...")
    update_all_stocks_cache(client)

    # 再次尝试从缓存中获取
    if stock_code in _stock_names_cache:
        return _stock_names_cache[stock_code]

    # 如果还是没找到，返回股票代码
    print(f"警告: 股票 {stock_code} 未在市场列表中找到")
    _stock_names_cache[stock_code] = stock_code
    return stock_code

def update_all_stocks_cache(client=None):
    """更新所有股票的缓存（上海和深圳市场）"""
    try:
        if client is None:
            client = Quotes.factory(market='std')

        all_stocks = []

        # 获取上海市场股票
        print("正在获取上海市场股票列表...")
        sh_stocks = client.stocks(market=consts.MARKET_SH)
        if sh_stocks is not None and not sh_stocks.empty:
            all_stocks.append(sh_stocks)
            print(f"获取到上海市场股票 {len(sh_stocks)} 只")

        # 获取深圳市场股票
        print("正在获取深圳市场股票列表...")
        sz_stocks = client.stocks(market=consts.MARKET_SZ)
        if sz_stocks is not None and not sz_stocks.empty:
            all_stocks.append(sz_stocks)
            print(f"获取到深圳市场股票 {len(sz_stocks)} 只")

        if all_stocks:
            # 合并所有股票数据
            combined_stocks = pd.concat(all_stocks, ignore_index=True)

            # 更新内存缓存
            for _, row in combined_stocks.iterrows():
                code = str(row['code']).zfill(6)
                name = row['name']
                _stock_names_cache[code] = name

            # 保存到CSV文件
            save_all_stocks_to_csv(combined_stocks)
            print(f"✓ 成功缓存 {len(combined_stocks)} 只股票信息")
        else:
            print("❌ 未能获取到任何股票数据")

    except Exception as e:
        print(f"❌ 更新股票缓存失败: {e}")

def load_stock_name_from_csv(stock_code):
    """从CSV文件中加载股票名称"""
    csv_file = "stock_names.csv"
    try:
        # 确保股票代码是6位字符串格式
        stock_code = str(stock_code).zfill(6)

        if os.path.exists(csv_file):
            df = pd.read_csv(csv_file, dtype={'code': str})  # 确保code列为字符串类型
            if 'code' in df.columns and 'name' in df.columns:
                # 同时加载所有股票到内存缓存
                for _, row in df.iterrows():
                    _stock_names_cache[row['code']] = row['name']

                # 返回请求的股票名称
                return _stock_names_cache.get(stock_code)
        return None
    except Exception as e:
        print(f"从CSV读取股票名称失败: {e}")
        return None

def save_all_stocks_to_csv(stocks_df):
    """保存所有股票信息到CSV文件"""
    csv_file = "stock_names.csv"
    try:
        # 确保股票代码是6位字符串格式
        stocks_df = stocks_df.copy()
        stocks_df['code'] = stocks_df['code'].astype(str).str.zfill(6)

        # 只保留需要的列
        result_df = stocks_df[['code', 'name']].copy()

        # 去重并排序
        result_df = result_df.drop_duplicates(subset=['code']).sort_values('code')

        # 保存到CSV文件
        result_df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"✓ 已保存 {len(result_df)} 只股票信息到 {csv_file}")

    except Exception as e:
        print(f"保存股票信息到CSV失败: {e}")

def get_real_time_price(stock_code, client):
    """获取实时股票价格"""
    try:
        # 首先尝试获取实时行情数据
        quotes_data = client.quotes(symbol=[stock_code])

        if quotes_data is not None and not quotes_data.empty:
            # 获取第一行数据
            quote = quotes_data.iloc[0]

            # 根据文档和实际测试，查找价格字段
            # 常见的价格字段名
            price_fields = ['price', 'last_price', 'current_price', '现价', '最新价', 'close']

            for field in price_fields:
                if field in quote.index and quote[field] is not None:
                    try:
                        price = float(quote[field])
                        if price > 0:
                            return price
                    except (ValueError, TypeError):
                        continue

        # 如果实时数据无效，尝试获取最新的K线数据（包含当天数据）
        print(f"实时价格无效，尝试获取 {stock_code} 的最新K线数据...")
        return get_latest_close_price(stock_code, client)

    except Exception as e:
        print(f"获取 {stock_code} 实时价格失败: {e}")
        # 尝试K线数据作为备选
        return get_latest_close_price(stock_code, client)

def get_latest_close_price(stock_code, client):
    """获取最新的收盘价作为参考价格，包含当天数据"""
    try:
        import datetime

        # 尝试多种方法获取最新数据
        methods = [
            # 方法1: 获取更多天数的日K线数据，确保包含当天
            {'frequency': 9, 'offset': 10},  # 获取最近10天数据
            # 方法2: 使用分钟K线获取当天数据
            {'frequency': 7, 'offset': 100}, # 获取最近100分钟数据
            # 方法3: 使用5分钟K线
            {'frequency': 0, 'offset': 50},  # 获取最近50个5分钟数据
        ]

        for method in methods:
            try:
                kline_data = client.bars(
                    symbol=stock_code,
                    frequency=method['frequency'],
                    offset=method['offset']
                )

                if kline_data is not None and not kline_data.empty:
                    # 获取最新的收盘价
                    latest_data = kline_data.iloc[-1]
                    close_price = latest_data.get('close', 0)

                    if close_price > 0:
                        stock_name = get_stock_name(stock_code)

                        # 获取数据日期
                        data_date = latest_data.get('datetime', 'Unknown')
                        if hasattr(data_date, 'strftime'):
                            date_str = data_date.strftime('%Y-%m-%d %H:%M')
                        else:
                            date_str = str(data_date)

                        freq_name = {9: '日K', 7: '1分钟K', 0: '5分钟K'}.get(method['frequency'], 'K线')
                        print(f"使用 {stock_code}({stock_name}) 的最新{freq_name}收盘价: {close_price:.2f} (时间: {date_str})")
                        return float(close_price)

            except Exception as e:
                print(f"方法 {method} 获取 {stock_code} 数据失败: {e}")
                continue

        return None
    except Exception as e:
        print(f"获取 {stock_code} K线数据失败: {e}")
        return None

def calculate_optimal_allocation(stock_data, max_capital=20000):
    """
    计算股票的最优分配方案

    Args:
        stock_data: List of tuples (stock_code, price)
        max_capital: 总资金

    Returns:
        Dict containing allocation results
    """
    if not stock_data:
        return None

    # 解包股票代码和价格
    codes, prices = zip(*stock_data)
    n = len(codes)
    target_allocation = max_capital / n  # 目标分配金额
    shares = []
    investments = []

    # 第一轮：为每只股票分配基础股数（股数必须是100的整数倍）
    for price in prices:
        # 计算可购买的手数（1手=100股）
        hands = int(target_allocation // (price * 100))
        share = hands * 100  # 转换为股数
        shares.append(share)
        investments.append(share * price)

    # 计算剩余资金
    total_invested = sum(investments)
    remaining = max_capital - total_invested

    # 第二轮：智能分配剩余资金，确保占比均衡
    while remaining >= min(prices) * 100:
        # 计算当前各股票的资金占比
        current_ratios = [inv / max_capital * 100 for inv in investments]
        target_ratio = 100 / n  # 目标占比

        # 找到占比最低且还能增加100股的股票
        best_idx = -1
        min_ratio = float('inf')

        for i, (ratio, price) in enumerate(zip(current_ratios, prices)):
            # 检查是否还能增加100股
            if remaining >= price * 100:
                # 计算增加100股后的占比
                new_investment = investments[i] + price * 100
                new_ratio = new_investment / max_capital * 100

                # 确保增加后不会超出目标占比太多（允许5%的差异）
                if new_ratio <= target_ratio + 5 and ratio < min_ratio:
                    min_ratio = ratio
                    best_idx = i

        # 如果找到合适的股票，增加100股
        if best_idx != -1:
            shares[best_idx] += 100
            investments[best_idx] += prices[best_idx] * 100
            remaining -= prices[best_idx] * 100
        else:
            # 如果没有找到合适的股票，按价格从低到高尝试分配
            sorted_indices = sorted(range(n), key=lambda i: prices[i])
            allocated_this_round = False

            for i in sorted_indices:
                if remaining >= prices[i] * 100:
                    new_investment = investments[i] + prices[i] * 100
                    new_ratio = new_investment / max_capital * 100

                    # 放宽限制，允许分配
                    if new_ratio <= target_ratio + 8:  # 稍微放宽限制
                        shares[i] += 100
                        investments[i] += prices[i] * 100
                        remaining -= prices[i] * 100
                        allocated_this_round = True
                        break

            # 如果这轮没有分配任何股票，退出循环
            if not allocated_this_round:
                break

    # 计算最终结果
    total = sum(investments)
    final_investments = []
    for code, price, share in zip(codes, prices, shares):
        investment = share * price
        final_investments.append(investment)

    # 计算资金占比分析
    ratios = [(inv / max_capital) * 100 for inv in final_investments]
    max_ratio = max(ratios)
    min_ratio = min(ratios)
    ratio_diff = max_ratio - min_ratio

    return {
        'codes': codes,
        'prices': prices,
        'shares': shares,
        'investments': final_investments,
        'total_invested': total,
        'remaining': max_capital - total,
        'utilization_rate': (total / max_capital) * 100,
        'ratios': ratios,
        'max_ratio': max_ratio,
        'min_ratio': min_ratio,
        'ratio_diff': ratio_diff,
        'is_balanced': ratio_diff <= 5
    }


def print_purchase_plan(allocation_result, title="股票购买方案", date_info=None):
    """
    打印购买方案

    Args:
        allocation_result: calculate_optimal_allocation 的返回结果
        title: 标题
        date_info: 日期信息
    """
    if not allocation_result:
        print("\n❌ 没有有效的分配结果")
        return

    print(f"\n{'='*80}")
    print(title)
    print(f"{'='*80}")
    print("股票代码\t股票名称\t\t价格(元)\t股数\t投入金额(元)\t资金占比(%)")
    print("-" * 90)

    # 显示每只股票的信息
    for code, price, share, investment, ratio in zip(
        allocation_result['codes'],
        allocation_result['prices'],
        allocation_result['shares'],
        allocation_result['investments'],
        allocation_result['ratios']
    ):
        stock_name = get_stock_name(code)
        # 调整股票名称显示长度，确保对齐
        name_display = stock_name[:8].ljust(8) if len(stock_name) > 8 else stock_name.ljust(8)
        print(f"{code}\t{name_display}\t{price:.2f}\t\t{share}\t{investment:.2f}\t\t{ratio:.1f}%")

    print("-" * 90)
    print(f"总投入金额：{allocation_result['total_invested']:.2f}元")
    print(f"剩余资金：{allocation_result['remaining']:.2f}元")
    print(f"资金利用率：{allocation_result['utilization_rate']:.1f}%")

    # 显示资金占比分析
    print(f"资金占比差异：{allocation_result['ratio_diff']:.1f}% (最高{allocation_result['max_ratio']:.1f}% - 最低{allocation_result['min_ratio']:.1f}%)")
    if allocation_result['is_balanced']:
        print("✓ 资金占比均衡，差异在5%以内")
    else:
        print("⚠ 资金占比差异超过5%")

    if date_info:
        print(f"注意：{date_info}")

    print(f"{'='*80}\n")


def calculate_purchase_plan(stock_codes, max_capital=20000):
    """计算股票购买方案"""
    print("股票购买方案计算器")
    print("="*50)
    print(f"总资金: {max_capital:.2f}元")
    print(f"股票数量: {len(stock_codes)}只")
    print("="*50)

    # 初始化行情客户端
    client = Quotes.factory(market='std')

    try:
        # 获取有效股票数据
        valid_data = []
        print("正在获取股票价格...")

        for code in stock_codes:
            stock_name = get_stock_name(code, client)
            print(f"获取 {code}({stock_name}) 价格中...")
            price = get_real_time_price(code, client)
            if price and price > 0:
                valid_data.append((code, price))
                print(f"✓ {code}({stock_name}): {price:.2f}元")
            else:
                print(f"✗ {code}({stock_name}): 无法获取有效价格")

        if not valid_data:
            print("\n❌ 没有获取到任何有效的股票数据")
            print("可能的原因:")
            print("1. 网络连接问题")
            print("2. 股票代码错误")
            print("3. 市场未开放")
            print("4. mootdx服务器问题")
            return

        # 计算购买方案
        print(f"\n成功获取 {len(valid_data)} 只股票价格，开始计算购买方案...")

        # 使用分离的计算逻辑
        allocation_result = calculate_optimal_allocation(valid_data, max_capital)

        # 打印结果
        print_purchase_plan(allocation_result)

    except Exception as e:
        print(f"❌ 计算购买方案时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            client.disconnect()
        except:
            pass

# 主程序
if __name__ == "__main__":
    # 默认股票列表，用户可以修改这里的股票代码
    stock_list = ['601187', '600016', '002121', '000531']  # 平安银行、万科A、招商银行、贵州茅台

    # 默认总资金，用户可以修改
    total_capital = 20000

    try:
        print("欢迎使用股票购买方案计算器")
        print("当前配置:")
        print(f"股票列表: {', '.join(stock_list)}")
        print(f"总资金: {total_capital}元")
        print("\n开始计算...")

        calculate_purchase_plan(stock_list, total_capital)

    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
