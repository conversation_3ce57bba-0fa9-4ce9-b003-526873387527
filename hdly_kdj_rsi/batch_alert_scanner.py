#!/usr/bin/env python3
"""
批量股票提醒扫描器

实现plan_v1.md中的全股票提醒功能：
1. 遍历现有的所有数据文件
2. 检查每个股票在指定日期是否满足提醒条件
3. 将所有满足条件的股票以及满足的条件情况输出到一个文件中
4. 将第三项结果发邮件告知我
5. 日期默认是当前交易日（或当天非交易日的话就取当前最新交易日）
6. 股票列表默认是所有股票，但可以指定一个文件，文件中每行一个股票代码

使用方法:
python batch_alert_scanner.py [选项]

选项:
  -d, --date DATE           指定检查日期 (YYYY-MM-DD)，默认使用最新交易日
  -s, --stocks FILE         指定股票列表文件，每行一个股票代码，默认扫描所有股票
  -o, --output FILE         指定输出文件路径，默认自动生成
  -e, --email EMAIL         指定收件人邮箱，默认使用配置文件中的邮箱
  --no-email               不发送邮件
  --json                   同时输出JSON格式结果
  --summary-only           只输出提醒摘要
  --no-parallel            禁用并行处理（默认启用）
  --max-workers N          设置最大工作线程数（默认4）
  -h, --help               显示帮助信息
"""

import argparse
import sys
import os
from datetime import datetime
from typing import Optional

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_scanner import scan_all_stocks
from result_exporter import export_results_to_text, export_results_to_json, export_alert_summary
from email_notifier import EmailNotifier


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="批量股票提醒扫描器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python batch_alert_scanner.py                    # 扫描所有股票，使用最新交易日
  python batch_alert_scanner.py -d 2025-07-25      # 扫描指定日期
  python batch_alert_scanner.py -s stocks.txt      # 扫描指定股票列表
  python batch_alert_scanner.py --no-email         # 不发送邮件
  python batch_alert_scanner.py --json             # 同时输出JSON格式
  python batch_alert_scanner.py --summary-only     # 只输出提醒摘要
  python batch_alert_scanner.py --no-parallel      # 禁用并行处理
  python batch_alert_scanner.py --max-workers 8    # 使用8个工作线程
        """
    )
    
    parser.add_argument(
        '-d', '--date',
        type=str,
        help='指定检查日期 (YYYY-MM-DD)，默认使用最新交易日'
    )
    
    parser.add_argument(
        '-s', '--stocks',
        type=str,
        help='指定股票列表文件，每行一个股票代码，默认扫描所有股票'
    )
    
    parser.add_argument(
        '-o', '--output',
        type=str,
        help='指定输出文件路径，默认自动生成'
    )
    
    parser.add_argument(
        '-e', '--email',
        type=str,
        help='指定收件人邮箱，默认使用配置文件中的邮箱'
    )
    
    parser.add_argument(
        '--no-email',
        action='store_true',
        help='不发送邮件'
    )
    
    parser.add_argument(
        '--json',
        action='store_true',
        help='同时输出JSON格式结果'
    )
    
    parser.add_argument(
        '--summary-only',
        action='store_true',
        help='只输出提醒摘要'
    )

    parser.add_argument(
        '--no-parallel',
        action='store_true',
        help='禁用并行处理（默认启用）'
    )

    parser.add_argument(
        '--max-workers',
        type=int,
        default=4,
        help='设置最大工作线程数（默认4）'
    )

    return parser.parse_args()


def send_email_notification(results, recipient: Optional[str] = None) -> bool:
    """
    发送邮件通知
    
    参数:
    results: 扫描结果
    recipient: 收件人邮箱，如果为None则使用默认邮箱
    
    返回:
    bool: 是否发送成功
    """
    try:
        # 初始化邮件通知器
        notifier = EmailNotifier()
        
        # 确定收件人
        if recipient is None:
            # 使用默认收件人（可以从配置文件读取或硬编码）
            recipient = "<EMAIL>"  # 默认收件人
        
        # 发送邮件
        success = notifier.send_alert_email(results, recipient)
        
        if success:
            print(f"✅ 邮件通知已发送到: {recipient}")
        else:
            print(f"❌ 邮件发送失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 邮件发送过程中出现错误: {e}")
        return False


def main():
    """主函数"""
    print("🔔 批量股票提醒扫描器")
    print("=" * 60)
    
    # 解析命令行参数
    args = parse_arguments()
    
    # 显示配置信息
    print("📋 扫描配置:")
    print(f"   检查日期: {args.date if args.date else '最新交易日'}")
    print(f"   股票列表: {args.stocks if args.stocks else '所有股票'}")
    print(f"   输出文件: {args.output if args.output else '自动生成'}")
    print(f"   发送邮件: {'否' if args.no_email else '是'}")
    if not args.no_email:
        print(f"   收件人: {args.email if args.email else '默认邮箱'}")
    print(f"   JSON格式: {'是' if args.json else '否'}")
    print(f"   仅摘要: {'是' if args.summary_only else '否'}")
    print(f"   并行处理: {'否' if args.no_parallel else '是'}")
    if not args.no_parallel:
        print(f"   工作线程: {args.max_workers}")
    print()
    
    try:
        # 1. 执行股票扫描
        print("🔍 开始扫描...")
        results = scan_all_stocks(
            target_date=args.date,
            stock_list_file=args.stocks,
            max_workers=args.max_workers,
            use_parallel=not args.no_parallel
        )
        
        if not results:
            print("❌ 扫描失败或没有结果")
            return 1
        
        # 2. 导出结果
        print("\n📄 导出结果...")
        output_files = []
        
        if args.summary_only:
            # 只输出摘要
            summary_file = export_alert_summary(results, args.output)
            if summary_file:
                output_files.append(summary_file)
        else:
            # 输出完整报告
            text_file = export_results_to_text(results, args.output)
            if text_file:
                output_files.append(text_file)
            
            # 如果需要JSON格式
            if args.json:
                json_file = export_results_to_json(results)
                if json_file:
                    output_files.append(json_file)
        
        # 3. 发送邮件通知
        if not args.no_email:
            print("\n📧 发送邮件通知...")
            email_success = send_email_notification(results, args.email)
            
            if not email_success:
                print("⚠️  邮件发送失败，但扫描结果已保存到文件")
        
        # 4. 显示完成信息
        print("\n✅ 扫描完成!")
        
        # 统计信息
        total_stocks = len(results)
        alert_stocks = len([r for r in results if "error" not in r and r['alert_triggered']])
        error_stocks = len([r for r in results if "error" in r])
        
        print(f"📊 扫描统计:")
        print(f"   总计股票: {total_stocks}")
        print(f"   触发提醒: {alert_stocks}")
        print(f"   检查错误: {error_stocks}")
        
        if output_files:
            print(f"📁 输出文件:")
            for file_path in output_files:
                print(f"   {file_path}")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断扫描")
        return 1
    except Exception as e:
        print(f"\n❌ 扫描过程中出现错误: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
