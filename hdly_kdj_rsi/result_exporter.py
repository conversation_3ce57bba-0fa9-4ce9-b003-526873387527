#!/usr/bin/env python3
"""
结果输出模块

将股票扫描结果输出到文件，格式化显示满足条件的股票信息
"""

import os
import json
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any


def format_alert_result(result: Dict[str, Any]) -> str:
    """
    格式化单个股票的提醒结果
    
    参数:
    result: 股票检查结果
    
    返回:
    str: 格式化的结果字符串
    """
    if "error" in result:
        return f"❌ {result.get('stock_code', 'Unknown')}: {result['error']}"
    
    stock_code = result['stock_code']
    check_date = result['check_date']
    
    if not result['alert_triggered']:
        return f"✅ {stock_code} ({check_date}): 未触发提醒"
    
    # 触发提醒的情况
    lines = [f"🔔 {stock_code} ({check_date}): 触发提醒"]
    
    # HDLY条件
    hdly_condition = "✓ 满足" if result['hdly_condition'] else "✗ 不满足"
    lines.append(f"   HDLY条件: {hdly_condition}")
    
    # KDJ_RSI条件
    kdj_condition = "✓ 满足" if result['kdj_rsi_condition'] else "✗ 不满足"
    lines.append(f"   KDJ_RSI条件: {kdj_condition} ({result['kdj_rsi_description']})")
    
    # 指标数据
    data = result['data']
    lines.append("   指标数据:")
    
    # HDLY值
    hdly_val = data['hdly']
    if abs(hdly_val) < 1e-6:
        hdly_str = f"{hdly_val:.2e}"
    else:
        hdly_str = f"{hdly_val:.4f}"
    hdly_rounded = round(hdly_val, 2)
    lines.append(f"     HDLY: {hdly_str} (保留2位小数: {hdly_rounded:.2f})")
    
    if data['hdly_prev'] is not None:
        hdly_prev_val = data['hdly_prev']
        if abs(hdly_prev_val) < 1e-6:
            hdly_prev_str = f"{hdly_prev_val:.2e}"
        else:
            hdly_prev_str = f"{hdly_prev_val:.4f}"
        hdly_prev_rounded = round(hdly_prev_val, 2)
        lines.append(f"     HDLY(前一天): {hdly_prev_str} (保留2位小数: {hdly_prev_rounded:.2f})")
    
    # J值
    lines.append(f"     J: {data['J']:.2f}")
    if data.get('J_prev') is not None and not pd.isna(data['J_prev']):
        lines.append(f"     J(前一天): {data['J_prev']:.2f}")
    
    # RSI值
    rsi_val = data['RSI']
    rsi_str = f"{rsi_val:.2f}" if not pd.isna(rsi_val) else "N/A"
    lines.append(f"     RSI: {rsi_str}")
    if data.get('RSI_prev') is not None and not pd.isna(data['RSI_prev']):
        lines.append(f"     RSI(前一天): {data['RSI_prev']:.2f}")
    
    # KD值
    lines.append(f"     KD: {data['KD']:.2f}")
    if data.get('KD_prev') is not None and not pd.isna(data['KD_prev']):
        lines.append(f"     KD(前一天): {data['KD_prev']:.2f}")
    
    return "\n".join(lines)


def export_results_to_text(results: List[Dict[str, Any]], 
                          output_file: str = None) -> str:
    """
    将结果导出到文本文件
    
    参数:
    results: 检查结果列表
    output_file: 输出文件路径，如果为None则自动生成
    
    返回:
    str: 输出文件路径
    """
    # 生成输出文件名
    if output_file is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"stock_alert_results_{timestamp}.txt"
    
    # 统计信息
    total_stocks = len(results)
    alert_stocks = [r for r in results if "error" not in r and r['alert_triggered']]
    error_stocks = [r for r in results if "error" in r]
    normal_stocks = [r for r in results if "error" not in r and not r['alert_triggered']]
    
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 生成报告内容
    lines = [
        "=" * 80,
        "股票提醒扫描结果报告",
        "=" * 80,
        f"生成时间: {current_time}",
        f"总计股票: {total_stocks} 只",
        f"触发提醒: {len(alert_stocks)} 只",
        f"状态正常: {len(normal_stocks)} 只",
        f"检查错误: {len(error_stocks)} 只",
        "",
    ]
    
    # 添加触发提醒的股票
    if alert_stocks:
        lines.extend([
            "🔔 触发提醒的股票:",
            "-" * 80,
        ])
        for result in alert_stocks:
            lines.append(format_alert_result(result))
            lines.append("")
    
    # 添加错误信息
    if error_stocks:
        lines.extend([
            "❌ 检查错误的股票:",
            "-" * 80,
        ])
        for result in error_stocks:
            lines.append(format_alert_result(result))
            lines.append("")
    
    # 如果只有正常股票，添加简要说明
    if not alert_stocks and not error_stocks:
        lines.extend([
            "✅ 所有股票状态正常",
            "当前没有股票触发提醒条件。",
            "",
        ])
    
    lines.extend([
        "=" * 80,
        "报告结束",
        "=" * 80,
    ])
    
    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(lines))
        
        print(f"✅ 结果已导出到: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ 导出结果失败: {e}")
        return None


def export_results_to_json(results: List[Dict[str, Any]], 
                          output_file: str = None) -> str:
    """
    将结果导出到JSON文件
    
    参数:
    results: 检查结果列表
    output_file: 输出文件路径，如果为None则自动生成
    
    返回:
    str: 输出文件路径
    """
    # 生成输出文件名
    if output_file is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"stock_alert_results_{timestamp}.json"
    
    # 准备导出数据
    export_data = {
        "export_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "summary": {
            "total_stocks": len(results),
            "alert_stocks": len([r for r in results if "error" not in r and r['alert_triggered']]),
            "normal_stocks": len([r for r in results if "error" not in r and not r['alert_triggered']]),
            "error_stocks": len([r for r in results if "error" in r])
        },
        "results": results
    }
    
    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ JSON结果已导出到: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ 导出JSON结果失败: {e}")
        return None


def export_alert_summary(results: List[Dict[str, Any]], 
                        output_file: str = None) -> str:
    """
    导出提醒摘要（只包含触发提醒的股票）
    
    参数:
    results: 检查结果列表
    output_file: 输出文件路径，如果为None则自动生成
    
    返回:
    str: 输出文件路径
    """
    # 筛选触发提醒的股票
    alert_stocks = [r for r in results if "error" not in r and r['alert_triggered']]
    
    if not alert_stocks:
        print("📝 没有股票触发提醒，不生成摘要文件")
        return None
    
    # 生成输出文件名
    if output_file is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"alert_summary_{timestamp}.txt"
    
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 生成摘要内容
    lines = [
        "🔔 股票提醒摘要",
        "=" * 50,
        f"生成时间: {current_time}",
        f"触发提醒股票数量: {len(alert_stocks)}",
        "",
        "提醒股票列表:",
        "-" * 50,
    ]
    
    for result in alert_stocks:
        stock_code = result['stock_code']
        check_date = result['check_date']
        
        # 简化的条件描述
        conditions = []
        if result['hdly_condition']:
            conditions.append("HDLY")
        if result['kdj_rsi_condition']:
            conditions.append("KDJ_RSI")
        
        condition_str = " + ".join(conditions)
        lines.append(f"• {stock_code} ({check_date}) - {condition_str}")
    
    lines.extend([
        "",
        "=" * 50,
        "详细信息请查看完整报告",
    ])
    
    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(lines))
        
        print(f"✅ 提醒摘要已导出到: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ 导出提醒摘要失败: {e}")
        return None


if __name__ == "__main__":
    # 测试代码
    print("结果导出模块测试")
    
    # 创建测试数据
    test_results = [
        {
            "stock_code": "600066",
            "check_date": "2025-07-25",
            "hdly_condition": True,
            "kdj_rsi_condition": True,
            "kdj_rsi_description": "J值(15.50) > 前一个J值(12.30) 且 J值(15.50) < 20 且前一个J值是前3天最低点",
            "alert_triggered": True,
            "data": {
                "hdly": 0.1200,
                "hdly_prev": 0.1000,
                "J": 15.50,
                "J_prev": 12.30,
                "RSI": 25.80,
                "RSI_prev": 23.50,
                "KD": 18.20,
                "KD_prev": 16.10
            }
        },
        {
            "stock_code": "000001",
            "check_date": "2025-07-25",
            "hdly_condition": False,
            "kdj_rsi_condition": False,
            "kdj_rsi_description": "J值(25.80) >= 20",
            "alert_triggered": False,
            "data": {
                "hdly": 0.0800,
                "hdly_prev": 0.0900,
                "J": 25.80,
                "J_prev": 24.50,
                "RSI": 35.20,
                "RSI_prev": 33.10,
                "KD": 28.90,
                "KD_prev": 27.60
            }
        }
    ]
    
    # 测试导出功能
    text_file = export_results_to_text(test_results, "test_results.txt")
    json_file = export_results_to_json(test_results, "test_results.json")
    summary_file = export_alert_summary(test_results, "test_summary.txt")
    
    print(f"文本文件: {text_file}")
    print(f"JSON文件: {json_file}")
    print(f"摘要文件: {summary_file}")
