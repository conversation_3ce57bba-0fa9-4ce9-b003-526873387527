## 任务
在 plan.md 的基础上，增加一个全股票的指定日期的提醒功能

## 提醒条件（已更新）
1. hdly 指标值大于 0.1 且当前指标值大于前一个指标值
2. kdj_rsi 指标中的 J 值大于前一个 J 值 且 当前 J 值低于 20 且前一个 J 值相对其前 5 天来看是最低点且当前 J 值与前一个 J 值连线角度大于 75 度

## 具体要求
1. 遍历现有的所有数据文件
2. 检查每个股票在指定日期是否满足提醒条件
3. 将所有满足条件的股票以及满足的条件情况输出到一个文件中
4. 将第三项结果发邮件告知我
5. 日期默认是当前交易日（或当天非交易日的话就取当前最新交易日）
6. 股票列表默认是所有股票，但可以指定一个文件，文件中每行一个股票代码

## 规范
1. 所有代码在 hdly_kdj_rsi 文件夹内实现
2. 数据可以从根目录的 data 文件夹内读取，数据文件为 [股票代码].csv
3. 不影响已有的代码文件内容，请新建文件实现
