# 批量股票提醒扫描器

## 功能概述

批量股票提醒扫描器实现了 `plan_v1.md` 中的全股票提醒功能：

1. 遍历现有的所有数据文件
2. 检查每个股票在指定日期是否满足提醒条件
3. 将所有满足条件的股票以及满足的条件情况输出到一个文件中
4. 将第三项结果发邮件告知我
5. 日期默认是当前交易日（或当天非交易日的话就取当前最新交易日）
6. 股票列表默认是所有股票，但可以指定一个文件，文件中每行一个股票代码

## 提醒条件

根据 `plan.md` 中的定义：

1. **HDLY条件**：hdly指标值大于0.1且当前指标值大于前一个指标值
2. **KDJ_RSI条件**：J值大于前一个J值 且 J值小于20 且前一个J值相对其前3天来看是最低点

只有当两个条件都满足时，才会触发提醒。

## 使用方法

### 基本用法

```bash
# 扫描所有股票，使用最新交易日
python batch_alert_scanner.py

# 扫描指定日期的所有股票
python batch_alert_scanner.py -d 2025-07-25

# 扫描指定股票列表
python batch_alert_scanner.py -s stocks.txt

# 不发送邮件
python batch_alert_scanner.py --no-email

# 同时输出JSON格式
python batch_alert_scanner.py --json

# 只输出提醒摘要
python batch_alert_scanner.py --summary-only
```

### 命令行选项

- `-d, --date DATE`: 指定检查日期 (YYYY-MM-DD)，默认使用最新交易日
- `-s, --stocks FILE`: 指定股票列表文件，每行一个股票代码，默认扫描所有股票
- `-o, --output FILE`: 指定输出文件路径，默认自动生成
- `-e, --email EMAIL`: 指定收件人邮箱，默认使用配置文件中的邮箱
- `--no-email`: 不发送邮件
- `--json`: 同时输出JSON格式结果
- `--summary-only`: 只输出提醒摘要
- `-h, --help`: 显示帮助信息

### 股票列表文件格式

如果使用 `-s` 选项指定股票列表，文件格式应为每行一个股票代码：

```
600066
000001
000002
600036
```

## 输出文件

### 文本报告

默认生成文本格式的详细报告，包含：
- 扫描统计信息
- 触发提醒的股票详细信息
- 检查错误的股票信息

文件名格式：`stock_alert_results_YYYYMMDD_HHMMSS.txt`

### JSON报告

使用 `--json` 选项时，同时生成JSON格式的结构化数据，便于程序处理。

文件名格式：`stock_alert_results_YYYYMMDD_HHMMSS.json`

### 提醒摘要

使用 `--summary-only` 选项时，只生成简要的提醒摘要，包含触发提醒的股票列表。

文件名格式：`alert_summary_YYYYMMDD_HHMMSS.txt`

## 邮件通知

系统会自动发送邮件通知，包含：
- HTML格式的美观报告
- 触发提醒的股票详细信息
- 扫描统计信息

邮件配置使用项目根目录的 `email_config.json` 文件。

## 文件结构

```
hdly_kdj_rsi/
├── batch_alert_scanner.py    # 主程序入口
├── stock_scanner.py          # 股票扫描模块
├── alert_wrapper.py          # 提醒条件检查包装器
├── result_exporter.py        # 结果输出模块
├── email_notifier.py         # 邮件通知模块
├── alert_conditions.py       # 原始提醒条件模块
├── test_stocks.txt           # 测试用股票列表
└── README_batch_scanner.md   # 本说明文件
```

## 示例

### 扫描所有股票并发送邮件

```bash
python batch_alert_scanner.py
```

输出：
```
🔔 批量股票提醒扫描器
============================================================
📋 扫描配置:
   检查日期: 最新交易日
   股票列表: 所有股票
   输出文件: 自动生成
   发送邮件: 是
   收件人: 默认邮箱
   JSON格式: 否
   仅摘要: 否

🔍 开始扫描...
============================================================
开始全股票扫描
============================================================
从 /path/to/data 目录找到 2099 只股票
📅 检查日期: 2025-07-25
📊 股票数量: 2099
------------------------------------------------------------
[   1/2099] 检查股票 000001... ✅ 正常
[   2/2099] 检查股票 000002... ✅ 正常
...
------------------------------------------------------------
✅ 扫描完成
📊 总计: 2099 只股票
🔔 触发提醒: 5 只
❌ 检查错误: 0 只
============================================================

📄 导出结果...
✅ 结果已导出到: stock_alert_results_20250727_114700.txt

📧 发送邮件通知...
✅ 邮件通知已发送到: <EMAIL>

✅ 扫描完成!
📊 扫描统计:
   总计股票: 2099
   触发提醒: 5
   检查错误: 0
📁 输出文件:
   stock_alert_results_20250727_114700.txt
```

### 扫描指定股票列表

```bash
python batch_alert_scanner.py -s my_stocks.txt -d 2025-07-25 --json
```

这将扫描 `my_stocks.txt` 文件中列出的股票，检查2025-07-25的数据，并同时生成文本和JSON格式的报告。

## 注意事项

1. 确保 `email_config.json` 配置正确，以便邮件功能正常工作
2. 股票数据文件应位于项目根目录的 `data/` 文件夹中
3. 扫描大量股票时可能需要较长时间，请耐心等待
4. 如果某个股票的数据文件不存在或格式错误，会记录为检查错误，但不会中断整个扫描过程

## 故障排除

### 常见问题

1. **数据文件找不到**：确保股票数据文件位于正确的 `data/` 目录中
2. **邮件发送失败**：检查 `email_config.json` 配置和网络连接
3. **内存不足**：扫描大量股票时，可以分批处理或使用股票列表文件

### 调试模式

可以先用少量股票测试功能：

```bash
# 创建测试股票列表
echo -e "600066\n000001\n000002" > test_stocks.txt

# 测试扫描
python batch_alert_scanner.py -s test_stocks.txt --no-email
```
