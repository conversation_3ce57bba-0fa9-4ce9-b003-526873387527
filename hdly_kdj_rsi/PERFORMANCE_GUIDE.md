# 批量扫描器性能优化指南

## 🚀 性能优化特性

### 1. 并行处理
- **默认启用**：自动使用多线程并行处理
- **智能切换**：小于10只股票时自动使用串行处理
- **可配置**：支持自定义工作线程数

### 2. 性能监控
- **实时进度**：每100只股票显示进度
- **耗时统计**：显示总耗时和平均处理速度
- **资源使用**：显示并行处理配置信息

## 📊 性能对比

基于测试结果：

| 模式 | 股票数量 | 耗时 | 速度 |
|------|----------|------|------|
| 串行处理 | 10只 | 0.4秒 | 102股票/秒 |
| 并行处理(4线程) | 10只 | 0.3秒 | 109股票/秒 |
| 并行处理(4线程) | 2099只 | 21秒 | 100股票/秒 |
| 并行处理(8线程) | 2099只 | 21秒 | 103股票/秒 |

## 🔧 使用建议

### 1. 默认使用（推荐）
```bash
python batch_alert_scanner.py --no-email --summary-only
```
- 自动启用并行处理
- 使用4个工作线程
- 适合大多数场景

### 2. 高性能模式
```bash
python batch_alert_scanner.py --no-email --summary-only --max-workers 8
```
- 使用8个工作线程
- 适合CPU核心数较多的机器
- 处理大量股票时效果更明显

### 3. 兼容模式
```bash
python batch_alert_scanner.py --no-email --summary-only --no-parallel
```
- 禁用并行处理
- 使用传统串行模式
- 适合调试或资源受限环境

### 4. 自定义配置
```bash
python batch_alert_scanner.py --no-email --summary-only --max-workers 6
```
- 自定义工作线程数
- 建议设置为CPU核心数的1-2倍

## ⚡ 优化效果

### 主要改进：
1. **并行处理**：多线程同时处理多只股票
2. **智能调度**：自动选择最优处理模式
3. **进度显示**：实时显示处理进度
4. **性能监控**：详细的耗时和速度统计

### 适用场景：
- ✅ 大批量股票扫描（>100只）
- ✅ 日常定时扫描任务
- ✅ 多核CPU环境
- ✅ 需要快速获取结果

### 注意事项：
- 小批量股票（<10只）自动使用串行处理
- 线程数过多可能导致资源竞争
- I/O密集型任务，线程数建议为CPU核心数的1-2倍

## 🛠️ 故障排除

### 如果遇到性能问题：
1. 检查CPU使用率
2. 尝试调整 `--max-workers` 参数
3. 使用 `--no-parallel` 进行对比测试
4. 检查磁盘I/O性能

### 如果遇到错误：
1. 使用 `--no-parallel` 模式进行调试
2. 检查数据文件完整性
3. 确认系统资源充足
