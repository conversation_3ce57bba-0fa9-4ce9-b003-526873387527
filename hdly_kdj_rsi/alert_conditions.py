#!/usr/bin/env python3
"""
股票提醒条件检查模块

根据plan.md中的条件实现：
1. hdly指标值大于0.1且当前指标值大于前一个指标值
2. kdj_rsi指标中的J值大于前一个J值 且 J值小于20 且前一个J值相对其前3天来看是最低点 且当前J值与前一个J值连线角度大于60度

提醒条件说明：
- hdly条件：当前值 > 0.1 且 当前值 > 前一个值
- kdj_rsi条件：J值上升 且 J < 20 且 前一个J值是前3天最低点 且 连线角度 > 60°
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加父目录到路径以导入指标计算模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'hdly'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'kdj_rsi'))

from hdly.hdly_indicator import get_hdly_value
from kdj_rsi.kdj_rsi_indicator import get_kdj_rsi_values


def load_hdly_data(stock_code):
    """加载股票数据 - hdly版本"""
    try:
        file_path = f"data/{stock_code}.csv"
        data = pd.read_csv(file_path)
        data['date'] = pd.to_datetime(data['date'])
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None


def load_kdj_data(stock_code):
    """加载股票数据 - kdj版本"""
    try:
        file_path = f"data/{stock_code}.csv"
        data = pd.read_csv(file_path)
        data['date'] = pd.to_datetime(data['date'])
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None


def check_hdly_condition(hdly_values, index):
    """
    检查hdly指标的提醒条件

    条件：hdly指标值大于0.1且当前指标值大于前一个指标值
    解释：当前值 > 0.1 且 当前值 > 前一个值
    注意：比较时保留2位小数

    参数:
    hdly_values: pandas Series，hdly指标值序列
    index: int，当前检查的索引位置

    返回:
    bool: 是否满足条件
    """
    if index < 1:  # 需要至少有前一个值
        return False

    current_value = hdly_values.iloc[index]
    previous_value = hdly_values.iloc[index - 1]

    # 保留2位小数进行比较
    current_rounded = round(current_value, 2) if not pd.isna(current_value) else 0
    previous_rounded = round(previous_value, 2) if not pd.isna(previous_value) else 0

    # 检查当前值是否大于0.1
    if current_rounded <= 0.1:
        return False

    # 检查当前值是否大于前一个值
    condition_met = current_rounded > previous_rounded

    return condition_met


def check_kdj_rsi_condition(kdj_rsi_data, index):
    """
    检查kdj_rsi指标的提醒条件

    条件：J值大于前一个J值 且 当前J值低于20 且前一个J值相对其前5天来看是最低点 且当前J值与前一个J值连线角度大于75度

    参数:
    kdj_rsi_data: DataFrame，包含KD, J, RSI列
    index: int，当前检查的索引位置

    返回:
    tuple: (bool, str) - (是否满足条件, 条件描述)
    """
    if index < 4:  # 需要至少有前4个值（当前、前1、前2、前3、前4）
        return False, "数据不足，需要至少4个历史数据点"

    if index >= len(kdj_rsi_data):
        return False, "索引超出范围"

    current_j = kdj_rsi_data.iloc[index]['J']
    previous_j = kdj_rsi_data.iloc[index - 1]['J']

    # 检查当前J值和前一个J值是否有效
    if pd.isna(current_j) or pd.isna(previous_j):
        current_j_str = f"{current_j:.2f}" if not pd.isna(current_j) else "N/A"
        previous_j_str = f"{previous_j:.2f}" if not pd.isna(previous_j) else "N/A"
        return False, f"J值({current_j_str}) 或前一个J值({previous_j_str}) 无效"

    # 检查条件1：当前J值是否大于前一个J值
    j_rising = current_j > previous_j

    # 检查条件2：当前J值低于20
    j_below_20 = current_j < 20

    # 检查条件3：前一个J值相对其前5天来看是最低点
    # 获取前一个J值的前5天数据
    j_values_for_comparison = []
    for i in range(6):  # 前6天、前5天、前4天、前3天、前2天、前1天
        j_val = kdj_rsi_data.iloc[index - 1 - i]['J']
        if not pd.isna(j_val):
            j_values_for_comparison.append(j_val)

    # 如果没有足够的有效数据，返回False
    if len(j_values_for_comparison) < 6:
        return False, "前一个J值的历史数据不足"

    # 前一个J值是第一个元素，检查它是否是最小值
    previous_j_is_lowest = previous_j == min(j_values_for_comparison)

    # 检查条件4：当前J值与前一个J值连线角度大于75度
    # 计算角度：假设x轴为时间（1个单位），y轴为J值差
    j_diff = current_j - previous_j
    # 角度 = arctan(y_diff / x_diff) * 180 / π
    # 这里x_diff = 1（一个时间单位），所以角度 = arctan(j_diff) * 180 / π
    angle_radians = np.arctan(j_diff)
    angle_degrees = angle_radians * 180 / np.pi
    angle_condition = angle_degrees > 75

    # 四个条件都必须满足
    condition_met = j_rising and j_below_20 and previous_j_is_lowest and angle_condition

    if condition_met:
        return True, f"J值({current_j:.2f}) > 前一个J值({previous_j:.2f}) 且 J值({current_j:.2f}) < 20 且前一个J值是前5天最低点 且连线角度({angle_degrees:.1f}°) > 75°"
    else:
        if not j_rising:
            return False, f"J值({current_j:.2f}) <= 前一个J值({previous_j:.2f})"
        elif not j_below_20:
            return False, f"J值({current_j:.2f})不低于20"
        elif not previous_j_is_lowest:
            return False, f"前一个J值({previous_j:.2f})不是前5天最低点"
        else:
            return False, f"连线角度({angle_degrees:.1f}°) <= 75°"


def check_alert_conditions(stock_code, target_date=None):
    """
    检查指定股票的提醒条件
    
    参数:
    stock_code: str，股票代码
    target_date: str，目标日期 (YYYY-MM-DD)，如果为None则检查最新数据
    
    返回:
    dict: 检查结果
    """
    try:
        # 1. 加载并计算hdly指标
        hdly_data = load_hdly_data(stock_code)
        if hdly_data is None:
            return {"error": f"无法加载股票 {stock_code} 的数据"}
        
        hdly_values = get_hdly_value(hdly_data['low'])
        hdly_data['hdly'] = hdly_values
        
        # 2. 加载并计算kdj_rsi指标
        kdj_data = load_kdj_data(stock_code)
        from kdj_rsi.kdj_rsi_indicator import calculate_kdj_rsi
        kdj_rsi_data = calculate_kdj_rsi(kdj_data)
        
        # 3. 合并数据
        merged_data = pd.merge(
            hdly_data[['date', 'hdly']], 
            kdj_rsi_data[['date', 'KD', 'J', 'RSI']], 
            on='date', 
            how='inner'
        )
        
        if len(merged_data) == 0:
            return {"error": f"股票 {stock_code} 没有可用的指标数据"}
        
        # 4. 确定检查的日期和索引
        if target_date:
            target_date = pd.to_datetime(target_date)
            target_rows = merged_data[merged_data['date'] == target_date]
            if len(target_rows) == 0:
                return {"error": f"未找到日期 {target_date.strftime('%Y-%m-%d')} 的数据"}
            check_index = target_rows.index[0]
        else:
            # 检查最新数据
            check_index = len(merged_data) - 1
        
        # 5. 检查条件
        check_date = merged_data.iloc[check_index]['date']
        
        # 检查hdly条件
        hdly_condition = check_hdly_condition(merged_data['hdly'], check_index)
        
        # 检查kdj_rsi条件
        kdj_rsi_condition, kdj_rsi_desc = check_kdj_rsi_condition(merged_data, check_index)
        
        # 6. 组装结果
        result = {
            "stock_code": stock_code,
            "check_date": check_date.strftime('%Y-%m-%d'),
            "hdly_condition": hdly_condition,
            "kdj_rsi_condition": kdj_rsi_condition,
            "kdj_rsi_description": kdj_rsi_desc,
            "alert_triggered": hdly_condition and kdj_rsi_condition,
            "data": {
                "hdly": merged_data.iloc[check_index]['hdly'],
                "hdly_prev": merged_data.iloc[check_index - 1]['hdly'] if check_index > 0 else None,
                "J": merged_data.iloc[check_index]['J'],
                "J_prev": merged_data.iloc[check_index - 1]['J'] if check_index > 0 else None,
                "RSI": merged_data.iloc[check_index]['RSI'],
                "RSI_prev": merged_data.iloc[check_index - 1]['RSI'] if check_index > 0 else None,
                "KD": merged_data.iloc[check_index]['KD'],
                "KD_prev": merged_data.iloc[check_index - 1]['KD'] if check_index > 0 else None
            }
        }
        
        return result
        
    except Exception as e:
        return {"error": f"检查过程中出现错误: {str(e)}"}


if __name__ == "__main__":
    import sys

    # 测试代码
    print("股票提醒条件检查测试")
    print("=" * 50)

    # 从命令行参数获取股票代码和日期
    if len(sys.argv) >= 2:
        stock_code = sys.argv[1]
    else:
        stock_code = "600066"

    if len(sys.argv) >= 3:
        target_date = sys.argv[2]
    else:
        target_date = None

    result = check_alert_conditions(stock_code, target_date)
    
    if "error" in result:
        print(f"错误: {result['error']}")
    else:
        print(f"股票代码: {result['stock_code']}")
        print(f"检查日期: {result['check_date']}")
        print(f"HDLY条件: {'✓' if result['hdly_condition'] else '✗'}")
        print(f"KDJ_RSI条件: {'✓' if result['kdj_rsi_condition'] else '✗'} ({result['kdj_rsi_description']})")
        print(f"提醒触发: {'🔔 是' if result['alert_triggered'] else '❌ 否'}")
        print("\n指标数据:")
        # 显示HDLY值（原始值和保留2位小数后的值）
        hdly_val = result['data']['hdly']
        hdly_rounded = round(hdly_val, 2)
        if abs(hdly_val) < 1e-6:
            print(f"  HDLY: {hdly_val:.2e} (保留2位小数: {hdly_rounded:.2f})")
        else:
            print(f"  HDLY: {hdly_val:.4f} (保留2位小数: {hdly_rounded:.2f})")

        if result['data']['hdly_prev'] is not None:
            hdly_prev_val = result['data']['hdly_prev']
            hdly_prev_rounded = round(hdly_prev_val, 2)
            if abs(hdly_prev_val) < 1e-6:
                print(f"  HDLY(前一天): {hdly_prev_val:.2e} (保留2位小数: {hdly_prev_rounded:.2f})")
            else:
                print(f"  HDLY(前一天): {hdly_prev_val:.4f} (保留2位小数: {hdly_prev_rounded:.2f})")
        print(f"  J: {result['data']['J']:.2f}")
        if result['data'].get('J_prev') is not None and not pd.isna(result['data']['J_prev']):
            print(f"  J(前一天): {result['data']['J_prev']:.2f}")
        rsi_val = result['data']['RSI']
        rsi_str = f"{rsi_val:.2f}" if not pd.isna(rsi_val) else "N/A"
        print(f"  RSI: {rsi_str}")
        if result['data'].get('RSI_prev') is not None and not pd.isna(result['data']['RSI_prev']):
            print(f"  RSI(前一天): {result['data']['RSI_prev']:.2f}")
        print(f"  KD: {result['data']['KD']:.2f}")
        if result['data'].get('KD_prev') is not None and not pd.isna(result['data']['KD_prev']):
            print(f"  KD(前一天): {result['data']['KD_prev']:.2f}")
