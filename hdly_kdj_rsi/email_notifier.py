#!/usr/bin/env python3
"""
邮箱通知模块

实现股票提醒的邮箱通知功能，使用项目中的email_config.json配置
"""

import json
import smtplib
import os
import sys
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Head<PERSON>
from datetime import datetime
from typing import List, Dict, Optional
import pandas as pd


class EmailNotifier:
    """邮箱通知器"""
    
    def __init__(self, config_file: str = "email_config.json"):
        """
        初始化邮箱通知器
        
        参数:
        config_file: 邮箱配置文件路径
        """
        self.config = self._load_config(config_file)
        self.smtp_server = None
    
    def _load_config(self, config_file: str) -> Dict:
        """加载邮箱配置"""
        try:
            # 如果是相对路径，从项目根目录加载
            if not os.path.isabs(config_file):
                # 获取项目根目录
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.dirname(current_dir)
                config_file = os.path.join(project_root, config_file)
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证配置
            required_keys = ['host', 'port', 'auth']
            for key in required_keys:
                if key not in config:
                    raise ValueError(f"邮箱配置缺少必要字段: {key}")
            
            if 'user' not in config['auth'] or 'pass' not in config['auth']:
                raise ValueError("邮箱配置缺少认证信息")
            
            return config
            
        except FileNotFoundError:
            raise FileNotFoundError(f"邮箱配置文件不存在: {config_file}")
        except json.JSONDecodeError:
            raise ValueError(f"邮箱配置文件格式错误: {config_file}")
    
    def _connect_smtp(self):
        """连接SMTP服务器"""
        try:
            if self.config['port'] == 465:
                # SSL连接
                self.smtp_server = smtplib.SMTP_SSL(self.config['host'], self.config['port'])
            else:
                # 普通连接后启用TLS
                self.smtp_server = smtplib.SMTP(self.config['host'], self.config['port'])
                self.smtp_server.starttls()
            
            # 登录
            self.smtp_server.login(self.config['auth']['user'], self.config['auth']['pass'])
            return True
            
        except Exception as e:
            print(f"连接SMTP服务器失败: {e}")
            return False
    
    def _disconnect_smtp(self):
        """断开SMTP连接"""
        if self.smtp_server:
            try:
                self.smtp_server.quit()
            except:
                pass
            self.smtp_server = None
    
    def _format_alert_html(self, results: List[Dict]) -> str:
        """格式化提醒邮件HTML内容"""
        # 统计信息
        total_stocks = len(results)
        alert_stocks = [r for r in results if "error" not in r and r['alert_triggered']]
        error_stocks = [r for r in results if "error" in r]
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        html = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                .summary {{ background-color: #e8f4fd; padding: 10px; border-radius: 5px; margin-bottom: 20px; }}
                .alert {{ background-color: #fff2cc; border-left: 4px solid #ff9800; padding: 10px; margin: 10px 0; }}
                .normal {{ background-color: #f1f8e9; border-left: 4px solid #4caf50; padding: 10px; margin: 10px 0; }}
                .error {{ background-color: #ffebee; border-left: 4px solid #f44336; padding: 10px; margin: 10px 0; }}
                .data-table {{ border-collapse: collapse; width: 100%; margin-top: 10px; }}
                .data-table th, .data-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .data-table th {{ background-color: #f2f2f2; }}
                .icon {{ font-size: 18px; margin-right: 5px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🔔 股票提醒通知</h2>
                <p>检查时间: {current_time}</p>
            </div>
            
            <div class="summary">
                <h3>📊 检查汇总</h3>
                <ul>
                    <li>总计股票: {total_stocks} 只</li>
                    <li>触发提醒: <strong>{len(alert_stocks)} 只</strong></li>
                    <li>检查错误: {len(error_stocks)} 只</li>
                </ul>
            </div>
        """
        
        # 添加触发提醒的股票
        if alert_stocks:
            html += "<h3>🚨 触发提醒的股票</h3>"
            for result in alert_stocks:
                data = result['data']
                rsi_str = f"{data['RSI']:.2f}" if not pd.isna(data['RSI']) else "N/A"

                # 准备HDLY显示字符串
                hdly_val = data['hdly']
                if abs(hdly_val) < 1e-6:
                    hdly_current_str = f"{hdly_val:.2e}"
                else:
                    hdly_current_str = f"{hdly_val:.4f}"

                hdly_prev_val = data['hdly_prev']
                if hdly_prev_val is not None:
                    if abs(hdly_prev_val) < 1e-6:
                        hdly_prev_str = f"{hdly_prev_val:.2e}"
                    else:
                        hdly_prev_str = f"{hdly_prev_val:.4f}"
                    hdly_prev_rounded_str = f"(保留2位小数: {round(hdly_prev_val, 2):.2f})"
                else:
                    hdly_prev_str = "N/A"
                    hdly_prev_rounded_str = ""

                # 准备前一天的J、RSI、KD值显示字符串
                j_prev_str = f"{data['J_prev']:.2f}" if data['J_prev'] is not None and not pd.isna(data['J_prev']) else "N/A"
                rsi_prev_str = f"{data['RSI_prev']:.2f}" if data['RSI_prev'] is not None and not pd.isna(data['RSI_prev']) else "N/A"
                kd_prev_str = f"{data['KD_prev']:.2f}" if data['KD_prev'] is not None and not pd.isna(data['KD_prev']) else "N/A"
                
                html += f"""
                <div class="alert">
                    <h4><span class="icon">🔔</span>股票 {result['stock_code']} ({result['check_date']})</h4>
                    <p><strong>提醒原因:</strong></p>
                    <ul>
                        <li>HDLY条件: {'✓ 满足' if result['hdly_condition'] else '✗ 不满足'}</li>
                        <li>KDJ_RSI条件: {'✓ 满足' if result['kdj_rsi_condition'] else '✗ 不满足'} ({result['kdj_rsi_description']})</li>
                    </ul>
                    
                    <table class="data-table">
                        <tr>
                            <th>指标</th>
                            <th>当前值</th>
                            <th>前一天值</th>
                        </tr>
                        <tr>
                            <td>HDLY</td>
                            <td>{hdly_current_str} (保留2位小数: {round(data['hdly'], 2):.2f})</td>
                            <td>{hdly_prev_str} {hdly_prev_rounded_str}</td>
                        </tr>
                        <tr>
                            <td>J值</td>
                            <td>{data['J']:.2f}</td>
                            <td>{j_prev_str}</td>
                        </tr>
                        <tr>
                            <td>RSI</td>
                            <td>{rsi_str}</td>
                            <td>{rsi_prev_str}</td>
                        </tr>
                        <tr>
                            <td>KD</td>
                            <td>{data['KD']:.2f}</td>
                            <td>{kd_prev_str}</td>
                        </tr>
                    </table>
                </div>
                """
        
        # 添加错误信息
        if error_stocks:
            html += "<h3>❌ 检查错误</h3>"
            for result in error_stocks:
                html += f"""
                <div class="error">
                    <p><strong>错误:</strong> {result['error']}</p>
                </div>
                """
        
        # 如果没有提醒，显示正常状态
        if not alert_stocks and not error_stocks:
            html += """
            <div class="normal">
                <h3><span class="icon">✅</span>所有股票状态正常</h3>
                <p>当前没有股票触发提醒条件。</p>
            </div>
            """
        
        html += """
            <hr>
            <p style="color: #666; font-size: 12px;">
                此邮件由股票提醒系统自动发送，请勿回复。<br>
                如需修改提醒设置，请联系系统管理员。
            </p>
        </body>
        </html>
        """
        
        return html
    
    def send_alert_email(self, results: List[Dict], recipient: str, subject: str = None) -> bool:
        """
        发送提醒邮件
        
        参数:
        results: 检查结果列表
        recipient: 收件人邮箱
        subject: 邮件主题，如果为None则自动生成
        
        返回:
        bool: 是否发送成功
        """
        try:
            # 连接SMTP服务器
            if not self._connect_smtp():
                return False
            
            # 生成邮件主题
            if subject is None:
                alert_count = len([r for r in results if "error" not in r and r['alert_triggered']])
                current_date = datetime.now().strftime('%Y-%m-%d')
                if alert_count > 0:
                    subject = f"🔔 股票提醒 - {alert_count}只股票触发提醒 ({current_date})"
                else:
                    subject = f"✅ 股票检查报告 - 状态正常 ({current_date})"
            
            # 创建邮件
            msg = MIMEMultipart('alternative')
            msg['From'] = self.config['auth']['user']
            msg['To'] = recipient
            msg['Subject'] = Header(subject, 'utf-8')
            
            # 生成HTML内容
            html_content = self._format_alert_html(results)
            html_part = MIMEText(html_content, 'html', 'utf-8')
            msg.attach(html_part)
            
            # 发送邮件
            self.smtp_server.send_message(msg)
            
            print(f"✅ 邮件发送成功: {recipient}")
            return True
            
        except Exception as e:
            print(f"❌ 邮件发送失败: {e}")
            return False
        
        finally:
            self._disconnect_smtp()
    
    def test_connection(self) -> bool:
        """测试邮箱连接"""
        print("测试邮箱连接...")
        success = self._connect_smtp()
        if success:
            print("✅ 邮箱连接测试成功")
        else:
            print("❌ 邮箱连接测试失败")
        self._disconnect_smtp()
        return success


if __name__ == "__main__":
    # 测试邮箱通知功能
    notifier = EmailNotifier()
    
    # 测试连接
    if notifier.test_connection():
        # 创建测试数据
        test_results = [
            {
                "stock_code": "600066",
                "check_date": "2025-07-25",
                "hdly_condition": True,
                "kdj_rsi_condition": False,
                "kdj_rsi_description": "J值(4.81) >= 0 且 RSI值(27.85) >= -10",
                "alert_triggered": False,
                "data": {
                    "hdly": 0.0000,
                    "hdly_prev": 0.0000,
                    "J": 4.81,
                    "RSI": 27.85,
                    "KD": 13.37
                }
            }
        ]
        
        # 发送测试邮件
        recipient = "<EMAIL>"  # 使用配置中的邮箱作为测试收件人
        print(f"发送测试邮件到: {recipient}")
        success = notifier.send_alert_email(test_results, recipient, "📧 股票提醒系统测试邮件")
        
        if success:
            print("✅ 测试邮件发送成功")
        else:
            print("❌ 测试邮件发送失败")
