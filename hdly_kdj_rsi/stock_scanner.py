#!/usr/bin/env python3
"""
全股票扫描模块

实现plan_v1.md中的功能：
1. 遍历现有的所有数据文件
2. 检查每个股票在指定日期是否满足提醒条件
3. 将所有满足条件的股票以及满足的条件情况输出到一个文件中
4. 将第三项结果发邮件告知我
5. 日期默认是当前交易日（或当天非交易日的话就取当前最新交易日）
6. 股票列表默认是所有股票，但可以指定一个文件，文件中每行一个股票代码
"""

import os
import glob
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 添加父目录到路径以导入现有模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 导入当前目录的模块
from alert_wrapper import check_alert_conditions_wrapper
from email_notifier import EmailNotifier


def get_all_stock_codes(data_dir: str = "data") -> List[str]:
    """
    从data目录获取所有股票代码
    
    参数:
    data_dir: 数据目录路径
    
    返回:
    List[str]: 股票代码列表
    """
    try:
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        data_path = os.path.join(project_root, data_dir)
        
        # 查找所有.csv文件
        csv_files = glob.glob(os.path.join(data_path, "*.csv"))
        
        # 提取股票代码（文件名去掉.csv后缀）
        stock_codes = []
        for file_path in csv_files:
            filename = os.path.basename(file_path)
            stock_code = filename.replace('.csv', '')
            stock_codes.append(stock_code)
        
        # 排序
        stock_codes.sort()
        
        print(f"从 {data_path} 目录找到 {len(stock_codes)} 只股票")
        return stock_codes
        
    except Exception as e:
        print(f"获取股票代码列表失败: {e}")
        return []


def load_stock_codes_from_file(file_path: str) -> List[str]:
    """
    从文件加载股票代码列表
    
    参数:
    file_path: 股票代码文件路径，每行一个股票代码
    
    返回:
    List[str]: 股票代码列表
    """
    try:
        stock_codes = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                stock_code = line.strip()
                if stock_code:  # 忽略空行
                    stock_codes.append(stock_code)
        
        print(f"从文件 {file_path} 加载了 {len(stock_codes)} 只股票")
        return stock_codes
        
    except FileNotFoundError:
        print(f"股票代码文件不存在: {file_path}")
        return []
    except Exception as e:
        print(f"加载股票代码文件失败: {e}")
        return []


def get_latest_trading_date(stock_codes: List[str], data_dir: str = "data") -> Optional[str]:
    """
    获取最新交易日期
    
    通过检查几个股票的数据文件来确定最新的交易日期
    
    参数:
    stock_codes: 股票代码列表
    data_dir: 数据目录路径
    
    返回:
    Optional[str]: 最新交易日期 (YYYY-MM-DD)，如果无法确定则返回None
    """
    try:
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        data_path = os.path.join(project_root, data_dir)
        
        latest_date = None
        
        # 检查前几个股票的数据来确定最新日期
        for stock_code in stock_codes[:5]:  # 只检查前5个股票
            try:
                file_path = os.path.join(data_path, f"{stock_code}.csv")
                if os.path.exists(file_path):
                    data = pd.read_csv(file_path)
                    if len(data) > 0:
                        data['date'] = pd.to_datetime(data['date'])
                        max_date = data['date'].max()
                        
                        if latest_date is None or max_date > latest_date:
                            latest_date = max_date
            except Exception as e:
                print(f"检查股票 {stock_code} 的数据时出错: {e}")
                continue
        
        if latest_date:
            latest_date_str = latest_date.strftime('%Y-%m-%d')
            print(f"确定最新交易日期为: {latest_date_str}")
            return latest_date_str
        else:
            print("无法确定最新交易日期")
            return None
            
    except Exception as e:
        print(f"获取最新交易日期失败: {e}")
        return None


def get_target_date(target_date: Optional[str] = None, stock_codes: List[str] = None) -> Optional[str]:
    """
    获取目标检查日期
    
    参数:
    target_date: 指定的目标日期，如果为None则使用最新交易日
    stock_codes: 股票代码列表，用于确定最新交易日
    
    返回:
    Optional[str]: 目标日期 (YYYY-MM-DD)
    """
    if target_date:
        # 验证日期格式
        try:
            datetime.strptime(target_date, '%Y-%m-%d')
            print(f"使用指定日期: {target_date}")
            return target_date
        except ValueError:
            print(f"日期格式错误: {target_date}，应为 YYYY-MM-DD 格式")
            return None
    
    # 使用最新交易日
    if stock_codes:
        return get_latest_trading_date(stock_codes)
    else:
        print("无法确定目标日期：缺少股票代码列表")
        return None


def check_single_stock(stock_code: str, check_date: str) -> Dict[str, Any]:
    """
    检查单个股票的提醒条件

    参数:
    stock_code: 股票代码
    check_date: 检查日期

    返回:
    Dict: 检查结果
    """
    try:
        result = check_alert_conditions_wrapper(stock_code, check_date)
        return result
    except Exception as e:
        return {
            "stock_code": stock_code,
            "error": f"检查过程中出现异常: {str(e)}"
        }


def scan_all_stocks(target_date: Optional[str] = None,
                   stock_list_file: Optional[str] = None,
                   data_dir: str = "data",
                   max_workers: int = 4,
                   use_parallel: bool = True) -> List[Dict[str, Any]]:
    """
    扫描所有股票的提醒条件

    参数:
    target_date: 目标检查日期，如果为None则使用最新交易日
    stock_list_file: 股票列表文件路径，如果为None则扫描所有股票
    data_dir: 数据目录路径
    max_workers: 并行处理的最大工作线程数
    use_parallel: 是否使用并行处理

    返回:
    List[Dict]: 检查结果列表
    """
    print("=" * 60)
    print("开始全股票扫描")
    print("=" * 60)
    
    # 1. 获取股票列表
    if stock_list_file:
        stock_codes = load_stock_codes_from_file(stock_list_file)
    else:
        stock_codes = get_all_stock_codes(data_dir)
    
    if not stock_codes:
        print("❌ 没有找到股票代码，扫描终止")
        return []
    
    # 2. 确定目标日期
    check_date = get_target_date(target_date, stock_codes)
    if not check_date:
        print("❌ 无法确定检查日期，扫描终止")
        return []
    
    print(f"📅 检查日期: {check_date}")
    print(f"📊 股票数量: {len(stock_codes)}")
    print(f"🔧 并行处理: {'是' if use_parallel else '否'}")
    if use_parallel:
        print(f"🧵 工作线程: {max_workers}")
    print("-" * 60)

    # 3. 检查股票（支持并行处理）
    results = []
    alert_count = 0
    error_count = 0
    start_time = time.time()

    if use_parallel and len(stock_codes) > 10:
        # 并行处理
        print("🚀 使用并行处理模式...")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_stock = {
                executor.submit(check_single_stock, stock_code, check_date): stock_code
                for stock_code in stock_codes
            }

            # 处理完成的任务
            completed = 0
            for future in as_completed(future_to_stock):
                stock_code = future_to_stock[future]
                completed += 1

                try:
                    result = future.result()
                    results.append(result)

                    # 每100个股票显示一次进度
                    if completed % 100 == 0 or completed == len(stock_codes):
                        print(f"[{completed:4d}/{len(stock_codes)}] 已完成...")

                    if "error" in result:
                        error_count += 1
                    elif result.get('alert_triggered', False):
                        alert_count += 1
                        print(f"🔔 {stock_code} 触发提醒")

                except Exception as e:
                    error_result = {
                        "stock_code": stock_code,
                        "error": f"检查过程中出现异常: {str(e)}"
                    }
                    results.append(error_result)
                    error_count += 1
                    print(f"❌ {stock_code} 异常: {str(e)}")
    else:
        # 串行处理
        print("🐌 使用串行处理模式...")

        for i, stock_code in enumerate(stock_codes, 1):
            print(f"[{i:4d}/{len(stock_codes)}] 检查股票 {stock_code}...", end=" ")

            result = check_single_stock(stock_code, check_date)
            results.append(result)

            if "error" in result:
                print("❌ 错误")
                error_count += 1
            elif result.get('alert_triggered', False):
                print("🔔 触发提醒")
                alert_count += 1
            else:
                print("✅ 正常")

    end_time = time.time()
    elapsed_time = end_time - start_time

    print("-" * 60)
    print(f"✅ 扫描完成")
    print(f"📊 总计: {len(stock_codes)} 只股票")
    print(f"🔔 触发提醒: {alert_count} 只")
    print(f"❌ 检查错误: {error_count} 只")
    print(f"⏱️  耗时: {elapsed_time:.1f} 秒")
    if len(stock_codes) > 0:
        print(f"📈 平均速度: {len(stock_codes)/elapsed_time:.1f} 股票/秒")
    print("=" * 60)
    
    return results


if __name__ == "__main__":
    # 测试代码
    print("股票扫描模块测试")
    
    # 测试获取股票列表
    stock_codes = get_all_stock_codes()
    print(f"找到股票数量: {len(stock_codes)}")
    if stock_codes:
        print(f"前10只股票: {stock_codes[:10]}")
    
    # 测试获取最新交易日期
    if stock_codes:
        latest_date = get_latest_trading_date(stock_codes)
        print(f"最新交易日期: {latest_date}")
