# 股票提醒系统

基于 HDLY 和 KDJ_RSI 技术指标的股票提醒系统，支持邮件通知功能。

## 功能特性

- ✅ **技术指标分析**: 集成 HDLY 和 KDJ_RSI 指标
- ✅ **智能提醒条件**: 基于多重条件的提醒逻辑
- ✅ **邮件通知**: 支持 HTML 格式的邮件提醒
- ✅ **批量检查**: 支持多只股票同时检查
- ✅ **监控模式**: 持续监控股票状态
- ✅ **数据导出**: 支持 CSV 格式结果导出
- ✅ **日志记录**: 完整的操作日志

## 提醒条件

系统会在以下两个条件**同时满足**时触发提醒：

### 1. HDLY 指标条件
- 当前 HDLY 值 > 0.1 (保留2位小数后)
- 当前 HDLY 值 > 前一个 HDLY 值 (保留2位小数后)

**注意**: HDLY 指标比较时会将数值保留2位小数后再进行判断，避免极小浮点数差异的影响。

### 2. KDJ_RSI 指标条件
- J 值 > 前一个 J 值 **且**
- J 值 < 20 **且**
- 前一个 J 值相对其前3天来看是最低点

## 安装依赖

```bash
pip install pandas numpy talib
```

## 配置邮箱

系统使用项目根目录的 `email_config.json` 文件进行邮箱配置：

```json
{
    "host": "smtp.qq.com",
    "port": 465,
    "auth": {
        "user": "<EMAIL>",
        "pass": "your_app_password"
    }
}
```

**注意**: 
- QQ邮箱需要使用应用专用密码，不是登录密码
- 其他邮箱服务商请相应调整 host 和 port

## 使用方法

### 1. 检查单个股票

```bash
# 检查股票 600066 的最新状态
python stock_alert.py 600066

# 检查指定日期的状态
python stock_alert.py 600066 --date 2025-07-25
```

### 2. 批量检查多个股票

```bash
# 批量检查多只股票
python stock_alert.py 600066 000001 002001 --batch
```

### 3. 邮件提醒

```bash
# 检查并发送邮件提醒
python stock_alert.py 600066 --email <EMAIL>

# 批量检查并发送邮件
python stock_alert.py 600066 000001 --batch --email <EMAIL>
```

### 4. 持续监控模式

```bash
# 每60秒检查一次（默认）
python stock_alert.py 600066 --monitor

# 自定义检查间隔（秒）
python stock_alert.py 600066 --monitor --interval 300

# 监控模式 + 邮件提醒
python stock_alert.py 600066 --monitor --email <EMAIL>
```

### 5. 数据导出

```bash
# 保存结果到 CSV 文件
python stock_alert.py 600066 000001 --batch --output results.csv

# 同时导出和发送邮件
python stock_alert.py 600066 --output results.csv --email <EMAIL>
```

### 6. 高级选项

```bash
# 静默模式（只显示提醒结果）
python stock_alert.py 600066 --quiet

# 调整日志级别
python stock_alert.py 600066 --log-level DEBUG

# 查看帮助
python stock_alert.py --help
```

## 输出示例

### 控制台输出

```
🔔 股票 000001 (2025-07-25) - 提醒触发
  HDLY条件: ✓
  KDJ_RSI条件: ✓ (J值(-6.93) < 0)
  指标数据:
    HDLY: 0.0000
    HDLY(前一天): 0.0000
    J: -6.93
    RSI: 28.41
    KD: 13.37
```

### 邮件通知

系统会发送包含以下内容的 HTML 邮件：
- 📊 检查汇总统计
- 🚨 触发提醒的股票详情
- 📈 完整的技术指标数据表格
- ❌ 错误信息（如有）

## 文件结构

```
hdly_kdj_rsi/
├── README.md              # 使用文档
├── plan.md               # 项目计划
├── alert_conditions.py   # 提醒条件检查逻辑
├── stock_alert.py        # 主程序
├── email_notifier.py     # 邮件通知模块
└── stock_alerts.log      # 运行日志
```

## 数据要求

- 股票数据文件位于项目根目录的 `data/` 文件夹
- 文件命名格式: `{股票代码}.csv`
- 数据格式: 包含 date, open, high, low, close, volume 列
- 日期格式: YYYY-MM-DD

## 故障排除

### 1. 邮件发送失败
- 检查 `email_config.json` 配置是否正确
- 确认邮箱服务商的 SMTP 设置
- QQ邮箱需要开启 SMTP 服务并使用应用专用密码

### 2. 数据文件不存在
- 确认股票代码对应的 CSV 文件存在于 `data/` 目录
- 检查文件名格式是否正确

### 3. 指标计算错误
- 确认数据文件包含必要的 OHLCV 列
- 检查数据格式和日期格式是否正确

## 测试

使用股票 600066 进行功能测试：

```bash
# 基本功能测试
python stock_alert.py 600066

# 邮件功能测试
python stock_alert.py 600066 --email <EMAIL>

# 批量功能测试
python stock_alert.py 600066 000001 --batch
```

## 技术说明

- **HDLY 指标**: 基于 Futu 风格的 SMA 计算和 EMA 的自定义技术指标
- **KDJ_RSI 指标**: 结合 KDJ 和 RSI 的复合指标，使用 Pine Script 兼容的 RMA 函数
- **数据处理**: 使用 pandas 进行数据处理和分析
- **邮件发送**: 基于 Python 标准库 smtplib 实现

## 许可证

本项目仅供学习和研究使用。
