#!/usr/bin/env python3
"""
分析600066股票从2024-09-10到现在的指标分布情况
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from alert_conditions import check_alert_conditions


def analyze_indicators(stock_code, start_date, end_date=None):
    """
    分析指定日期范围内的指标分布情况
    """
    print(f"分析股票 {stock_code} 从 {start_date} 到 {end_date or '最新'} 的指标分布")
    print("=" * 80)
    
    # 加载股票数据获取可用日期
    try:
        data_file = f"../data/{stock_code}.csv"
        if not os.path.exists(data_file):
            data_file = f"data/{stock_code}.csv"
        
        stock_data = pd.read_csv(data_file)
        stock_data['date'] = pd.to_datetime(stock_data['date'])
        
        # 过滤日期范围
        start_dt = pd.to_datetime(start_date)
        if end_date:
            end_dt = pd.to_datetime(end_date)
        else:
            end_dt = stock_data['date'].max()
        
        # 获取范围内的日期
        date_range = stock_data[
            (stock_data['date'] >= start_dt) & 
            (stock_data['date'] <= end_dt)
        ]['date'].sort_values()
        
        print(f"找到 {len(date_range)} 个交易日")
        print(f"日期范围: {date_range.min().strftime('%Y-%m-%d')} 到 {date_range.max().strftime('%Y-%m-%d')}")
        print()
        
        # 收集所有指标数据
        all_data = []
        
        for i, date in enumerate(date_range):
            date_str = date.strftime('%Y-%m-%d')
            
            if i % 50 == 0:
                print(f"处理进度: {i+1}/{len(date_range)} ({(i+1)/len(date_range)*100:.1f}%)")
            
            try:
                result = check_alert_conditions(stock_code, date_str)
                
                if "error" not in result:
                    data = result['data']
                    all_data.append({
                        'date': date_str,
                        'hdly': data['hdly'],
                        'hdly_prev': data['hdly_prev'],
                        'hdly_rounded': round(data['hdly'], 2),
                        'hdly_prev_rounded': round(data['hdly_prev'], 2) if data['hdly_prev'] is not None else None,
                        'j': data['J'],
                        'rsi': data['RSI'],
                        'kd': data['KD'],
                        'hdly_condition': result['hdly_condition'],
                        'kdj_rsi_condition': result['kdj_rsi_condition'],
                        'alert_triggered': result['alert_triggered']
                    })
                    
            except Exception as e:
                print(f"处理 {date_str} 时出错: {e}")
        
        if not all_data:
            print("❌ 没有收集到有效数据")
            return
        
        df = pd.DataFrame(all_data)
        
        print(f"\n📊 指标统计分析 (共 {len(df)} 个交易日):")
        print("=" * 80)
        
        # HDLY指标分析
        print("🔹 HDLY指标分析:")
        print(f"  原始值范围: {df['hdly'].min():.2e} ~ {df['hdly'].max():.2e}")
        print(f"  保留2位小数后范围: {df['hdly_rounded'].min():.2f} ~ {df['hdly_rounded'].max():.2f}")
        print(f"  保留2位小数后 > 0 的天数: {(df['hdly_rounded'] > 0).sum()}")
        print(f"  保留2位小数后 = 0 的天数: {(df['hdly_rounded'] == 0).sum()}")
        
        # 前一天HDLY分析
        df_prev = df[df['hdly_prev_rounded'].notna()]
        if len(df_prev) > 0:
            print(f"  前一天保留2位小数后 ≠ 0 的天数: {(df_prev['hdly_prev_rounded'] != 0).sum()}")
            print(f"  前一天保留2位小数后 = 0 的天数: {(df_prev['hdly_prev_rounded'] == 0).sum()}")
        
        # J值分析
        print(f"\n🔹 J值分析:")
        print(f"  范围: {df['j'].min():.2f} ~ {df['j'].max():.2f}")

        # 计算J值上升的天数（当前J值 > 前一个J值）
        j_rising_count = 0
        for i in range(1, len(df)):
            if df.iloc[i]['j'] > df.iloc[i-1]['j']:
                j_rising_count += 1

        print(f"  J值上升的天数: {j_rising_count}")
        print(f"  J值下降或持平的天数: {len(df) - 1 - j_rising_count}")

        # RSI值分析
        print(f"\n🔹 RSI值分析:")
        rsi_valid = df['rsi'].dropna()
        if len(rsi_valid) > 0:
            print(f"  范围: {rsi_valid.min():.2f} ~ {rsi_valid.max():.2f}")
        
        # 条件满足情况
        print(f"\n🔹 条件满足情况:")
        print(f"  HDLY条件满足天数: {df['hdly_condition'].sum()}")
        print(f"  KDJ_RSI条件满足天数: {df['kdj_rsi_condition'].sum()}")
        print(f"  提醒触发天数: {df['alert_triggered'].sum()}")
        
        # 找出最接近触发条件的日期
        print(f"\n🔍 最接近触发条件的日期:")
        
        # J值最小的几天
        j_min_days = df.nsmallest(5, 'j')[['date', 'j', 'rsi', 'hdly_rounded', 'hdly_prev_rounded']]
        print(f"\nJ值最小的5天:")
        for _, row in j_min_days.iterrows():
            print(f"  {row['date']}: J={row['j']:.2f}, RSI={row['rsi']:.2f}, HDLY={row['hdly_rounded']:.2f}, HDLY_prev={row['hdly_prev_rounded']:.2f}")
        
        # RSI值最小的几天
        rsi_min_days = df.nsmallest(5, 'rsi')[['date', 'j', 'rsi', 'hdly_rounded', 'hdly_prev_rounded']]
        print(f"\nRSI值最小的5天:")
        for _, row in rsi_min_days.iterrows():
            print(f"  {row['date']}: J={row['j']:.2f}, RSI={row['rsi']:.2f}, HDLY={row['hdly_rounded']:.2f}, HDLY_prev={row['hdly_prev_rounded']:.2f}")
        
        # HDLY保留2位小数后最大的几天
        hdly_max_days = df.nlargest(5, 'hdly_rounded')[['date', 'j', 'rsi', 'hdly_rounded', 'hdly_prev_rounded']]
        print(f"\nHDLY保留2位小数后最大的5天:")
        for _, row in hdly_max_days.iterrows():
            print(f"  {row['date']}: J={row['j']:.2f}, RSI={row['rsi']:.2f}, HDLY={row['hdly_rounded']:.2f}, HDLY_prev={row['hdly_prev_rounded']:.2f}")
        
        # 检查J值上升的情况
        j_rising_dates = []
        for i in range(1, len(df)):
            current_row = df.iloc[i]
            previous_row = df.iloc[i-1]
            if current_row['j'] > previous_row['j']:
                j_rising_dates.append({
                    'date': current_row['date'],
                    'current_j': current_row['j'],
                    'previous_j': previous_row['j'],
                    'hdly_condition': current_row['hdly_condition'],
                    'alert_triggered': current_row['alert_triggered']
                })

        if len(j_rising_dates) > 0:
            print(f"\n🔔 J值上升的日期 ({len(j_rising_dates)} 天):")
            for item in j_rising_dates[:10]:  # 只显示前10个
                print(f"  {item['date']}: J={item['current_j']:.2f} > {item['previous_j']:.2f}, HDLY条件={'✓' if item['hdly_condition'] else '✗'}, 提醒={'✓' if item['alert_triggered'] else '✗'}")
            if len(j_rising_dates) > 10:
                print(f"  ... 还有 {len(j_rising_dates) - 10} 天")
        
        return df
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # 分析600066从2024-09-10到现在
    stock_code = "600066"
    start_date = "2024-09-10"
    
    df = analyze_indicators(stock_code, start_date)
    
    if df is not None:
        print(f"\n📊 分析完成!")
        print(f"详细数据已收集，共 {len(df)} 个交易日的数据")
    else:
        print("❌ 分析失败")
