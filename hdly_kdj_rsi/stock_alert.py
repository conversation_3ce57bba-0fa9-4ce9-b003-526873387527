#!/usr/bin/env python3
"""
股票提醒主程序

整合hdly和kdj_rsi指标计算，实现股票提醒功能。

使用方法:
1. 检查单个股票的提醒条件:
   python stock_alert.py 600066

2. 检查指定日期的提醒条件:
   python stock_alert.py 600066 --date 2025-07-25

3. 批量检查多个股票:
   python stock_alert.py 600066 000001 002001 --batch

4. 持续监控模式:
   python stock_alert.py 600066 --monitor --interval 60

5. 保存结果到文件:
   python stock_alert.py 600066 --output alerts.csv

6. 发送邮件提醒:
   python stock_alert.py 600066 --email <EMAIL>

7. 监控模式并发送邮件:
   python stock_alert.py 600066 --monitor --email <EMAIL>
"""

import argparse
import sys
import os
import time
import datetime
import pandas as pd
import logging
from typing import List, Dict, Optional

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from alert_conditions import check_alert_conditions
from email_notifier import EmailNotifier


def setup_logging(log_level='INFO'):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('stock_alerts.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)


def print_alert_result(result: Dict, show_details: bool = True):
    """打印提醒结果"""
    if "error" in result:
        print(f"❌ 错误: {result['error']}")
        return
    
    stock_code = result['stock_code']
    check_date = result['check_date']
    alert_triggered = result['alert_triggered']
    
    # 状态图标
    status_icon = "🔔" if alert_triggered else "✅"
    status_text = "提醒触发" if alert_triggered else "正常"
    
    print(f"\n{status_icon} 股票 {stock_code} ({check_date}) - {status_text}")
    
    if show_details:
        print(f"  HDLY条件: {'✓' if result['hdly_condition'] else '✗'}")
        print(f"  KDJ_RSI条件: {'✓' if result['kdj_rsi_condition'] else '✗'} ({result['kdj_rsi_description']})")
        
        data = result['data']
        print(f"  指标数据:")

        # 显示HDLY值（原始值和保留2位小数后的值）
        hdly_val = data['hdly']
        hdly_rounded = round(hdly_val, 2)
        if abs(hdly_val) < 1e-6:
            print(f"    HDLY: {hdly_val:.2e} (保留2位小数: {hdly_rounded:.2f})")
        else:
            print(f"    HDLY: {hdly_val:.4f} (保留2位小数: {hdly_rounded:.2f})")

        if data['hdly_prev'] is not None:
            hdly_prev_val = data['hdly_prev']
            hdly_prev_rounded = round(hdly_prev_val, 2)
            if abs(hdly_prev_val) < 1e-6:
                print(f"    HDLY(前一天): {hdly_prev_val:.2e} (保留2位小数: {hdly_prev_rounded:.2f})")
            else:
                print(f"    HDLY(前一天): {hdly_prev_val:.4f} (保留2位小数: {hdly_prev_rounded:.2f})")
        print(f"    J: {data['J']:.2f}")
        if data.get('J_prev') is not None and not pd.isna(data['J_prev']):
            print(f"    J(前一天): {data['J_prev']:.2f}")
        rsi_str = f"{data['RSI']:.2f}" if not pd.isna(data['RSI']) else "N/A"
        print(f"    RSI: {rsi_str}")
        if data.get('RSI_prev') is not None and not pd.isna(data['RSI_prev']):
            print(f"    RSI(前一天): {data['RSI_prev']:.2f}")
        print(f"    KD: {data['KD']:.2f}")
        if data.get('KD_prev') is not None and not pd.isna(data['KD_prev']):
            print(f"    KD(前一天): {data['KD_prev']:.2f}")


def check_single_stock(stock_code: str, target_date: Optional[str] = None, logger=None) -> Dict:
    """检查单个股票的提醒条件"""
    if logger:
        logger.info(f"检查股票 {stock_code} 的提醒条件...")
    
    result = check_alert_conditions(stock_code, target_date)
    
    if logger and "error" not in result:
        if result['alert_triggered']:
            logger.warning(f"🔔 股票 {stock_code} 触发提醒条件!")
        else:
            logger.info(f"✅ 股票 {stock_code} 正常")
    
    return result


def check_multiple_stocks(stock_codes: List[str], target_date: Optional[str] = None, logger=None) -> List[Dict]:
    """批量检查多个股票的提醒条件"""
    results = []
    
    print(f"开始批量检查 {len(stock_codes)} 只股票...")
    print("=" * 60)
    
    for i, stock_code in enumerate(stock_codes, 1):
        print(f"[{i}/{len(stock_codes)}] 检查 {stock_code}...")
        result = check_single_stock(stock_code, target_date, logger)
        results.append(result)
        
        # 简化输出
        if "error" in result:
            print(f"  ❌ 错误: {result['error']}")
        else:
            status = "🔔 提醒" if result['alert_triggered'] else "✅ 正常"
            print(f"  {status}")
    
    return results


def monitor_stocks(stock_codes: List[str], interval: int = 60, logger=None, email_recipient: str = None):
    """持续监控股票"""
    print(f"开始监控 {len(stock_codes)} 只股票，检查间隔: {interval}秒")
    if email_recipient:
        print(f"邮件提醒: {email_recipient}")
    print("按 Ctrl+C 停止监控")
    print("=" * 60)

    # 初始化邮件通知器
    email_notifier = None
    if email_recipient:
        try:
            email_notifier = EmailNotifier()
            if not email_notifier.test_connection():
                print("❌ 邮箱连接失败，将不发送邮件提醒")
                email_notifier = None
        except Exception as e:
            print(f"❌ 邮箱初始化失败: {e}")
            email_notifier = None

    try:
        while True:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"\n[{current_time}] 开始检查...")

            # 收集所有结果
            results = []
            alert_count = 0

            for stock_code in stock_codes:
                result = check_single_stock(stock_code, None, logger)
                results.append(result)

                if "error" not in result and result['alert_triggered']:
                    alert_count += 1
                    print_alert_result(result, show_details=False)

            if alert_count == 0:
                print(f"  所有股票正常 ({len(stock_codes)}只)")
            else:
                print(f"  发现 {alert_count} 只股票触发提醒!")

                # 发送邮件提醒
                if email_notifier and email_recipient:
                    try:
                        success = email_notifier.send_alert_email(results, email_recipient)
                        if success:
                            print(f"  📧 邮件提醒已发送到: {email_recipient}")
                        else:
                            print(f"  ❌ 邮件发送失败")
                    except Exception as e:
                        print(f"  ❌ 邮件发送异常: {e}")

            print(f"  下次检查时间: {(datetime.datetime.now() + datetime.timedelta(seconds=interval)).strftime('%H:%M:%S')}")
            time.sleep(interval)

    except KeyboardInterrupt:
        print("\n监控已停止")


def save_results_to_csv(results: List[Dict], output_file: str):
    """保存结果到CSV文件"""
    data_rows = []
    
    for result in results:
        if "error" in result:
            continue
        
        row = {
            'stock_code': result['stock_code'],
            'check_date': result['check_date'],
            'alert_triggered': result['alert_triggered'],
            'hdly_condition': result['hdly_condition'],
            'kdj_rsi_condition': result['kdj_rsi_condition'],
            'kdj_rsi_description': result['kdj_rsi_description'],
            'hdly_value': result['data']['hdly'],
            'hdly_prev_value': result['data']['hdly_prev'],
            'j_value': result['data']['J'],
            'rsi_value': result['data']['RSI'],
            'kd_value': result['data']['KD']
        }
        data_rows.append(row)
    
    if data_rows:
        df = pd.DataFrame(data_rows)
        df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"\n结果已保存到: {output_file}")
    else:
        print("\n没有有效数据可保存")


def main():
    parser = argparse.ArgumentParser(
        description='股票提醒系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s 600066                                    # 检查单个股票
  %(prog)s 600066 --date 2025-07-25                  # 检查指定日期
  %(prog)s 600066 000001 002001 --batch              # 批量检查
  %(prog)s 600066 --monitor --interval 60            # 监控模式
  %(prog)s 600066 --output alerts.csv                # 保存结果
        """
    )
    
    parser.add_argument('stock_codes', nargs='+', help='股票代码列表')
    parser.add_argument('--date', help='指定检查日期 (YYYY-MM-DD)')
    parser.add_argument('--batch', action='store_true', help='批量检查模式')
    parser.add_argument('--monitor', action='store_true', help='持续监控模式')
    parser.add_argument('--interval', type=int, default=60, help='监控间隔(秒) (默认: 60)')
    parser.add_argument('--output', '-o', help='输出文件路径 (CSV 格式)')
    parser.add_argument('--email', '-e', help='邮件提醒收件人地址')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                        help='日志级别 (默认: INFO)')
    parser.add_argument('--quiet', '-q', action='store_true', help='静默模式，只显示提醒结果')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging(args.log_level)
    
    try:
        if args.monitor:
            # 监控模式
            monitor_stocks(args.stock_codes, args.interval, logger, args.email)
        elif args.batch or len(args.stock_codes) > 1:
            # 批量检查模式
            results = check_multiple_stocks(args.stock_codes, args.date, logger)
            
            # 显示汇总
            alert_stocks = [r for r in results if "error" not in r and r['alert_triggered']]
            error_stocks = [r for r in results if "error" in r]
            
            print("\n" + "=" * 60)
            print("检查汇总:")
            print(f"  总计: {len(results)} 只股票")
            print(f"  触发提醒: {len(alert_stocks)} 只")
            print(f"  检查错误: {len(error_stocks)} 只")
            
            if alert_stocks and not args.quiet:
                print("\n触发提醒的股票:")
                for result in alert_stocks:
                    print_alert_result(result, show_details=True)
            
            # 保存结果
            if args.output:
                save_results_to_csv(results, args.output)

            # 发送邮件提醒
            if args.email:
                try:
                    email_notifier = EmailNotifier()
                    success = email_notifier.send_alert_email(results, args.email)
                    if success:
                        print(f"\n📧 邮件提醒已发送到: {args.email}")
                    else:
                        print(f"\n❌ 邮件发送失败")
                except Exception as e:
                    print(f"\n❌ 邮件发送异常: {e}")
                
        else:
            # 单个股票检查
            stock_code = args.stock_codes[0]
            result = check_single_stock(stock_code, args.date, logger)
            
            if not args.quiet:
                print_alert_result(result, show_details=True)
            
            # 保存结果
            if args.output:
                save_results_to_csv([result], args.output)

            # 发送邮件提醒
            if args.email:
                try:
                    email_notifier = EmailNotifier()
                    success = email_notifier.send_alert_email([result], args.email)
                    if success:
                        print(f"\n📧 邮件提醒已发送到: {args.email}")
                    else:
                        print(f"\n❌ 邮件发送失败")
                except Exception as e:
                    print(f"\n❌ 邮件发送异常: {e}")
        
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
