# 股票价格范围预测模块

本模块实现了基于技术指标的股票价格范围预测功能，根据 hdly 指标和 KDJ_RSI 指标的特定条件，计算满足条件的股票价格范围。

## 功能特性

- **技术指标计算**: 集成 hdly 和 KDJ_RSI 指标计算
- **条件检查**: 实现复杂的技术指标条件判断
- **价格范围预测**: 反推计算满足条件的价格范围
- **涨跌幅限制**: 自动识别主板/科创板并应用相应涨跌幅限制
- **批量处理**: 支持多只股票的批量预测
- **详细报告**: 提供详细的预测报告和分析

## 文件结构

```
predict/
├── README.md                    # 本文档
├── plan.md                     # 原始需求说明
├── price_range_calculator.py   # 核心价格范围计算模块
├── predict_main.py             # 主要接口函数
└── test_prediction.py          # 测试脚本
```

## 预测条件

### 1. HDLY 指标条件
- 当前 hdly 指标值 > 0.1
- 当前 hdly 指标值 > 前一个指标值

### 2. KDJ_RSI J值条件
- 当前 J 值 > 前一个 J 值（上升趋势）
- 当前 J 值 < 20（超卖区域）
- 前一个 J 值是前 5 天的最低点
- 当前 J 值与前一个 J 值连线角度 > 70°

### 3. 涨跌幅限制
- 主板股票：±10%
- 科创板股票（688开头）：±20%

## 安装依赖

```bash
pip install pandas numpy talib
```

## 使用方法

### 基本使用

```python
from predict_main import predict_price_range

# 预测单只股票的价格范围
result = predict_price_range('600066', '2025-06-07')

if result['success']:
    print(f"有效价格范围: {result['prediction_results']['price_range']}")
else:
    print(f"预测失败: {result.get('error', '未知错误')}")
```

### 详细预测报告

```python
from predict_main import predict_price_range, print_prediction_report

# 获取详细预测结果
result = predict_price_range('600066', '2025-06-07', detailed=True)

# 打印格式化报告
if result['success']:
    print_prediction_report(result)
```

### 批量预测

```python
from predict_main import batch_predict

# 批量预测多只股票
stock_list = ['600066', '000001', '000002']
batch_result = batch_predict(stock_list, '2025-06-07')

print(f"成功率: {batch_result['success_rate']}")
```

### 自动使用最新日期

```python
# 不指定日期，自动使用下一个交易日
result = predict_price_range('600066')
```

## API 参考

### predict_price_range(stock_code, target_date=None, detailed=True)

预测股票价格范围的主要接口函数。

**参数:**
- `stock_code` (str): 股票代码，如 '600066'
- `target_date` (str, optional): 目标日期，格式为 'YYYY-MM-DD'
- `detailed` (bool): 是否返回详细信息，默认 True

**返回:**
- `Dict`: 预测结果字典

**返回结果结构 (detailed=True):**
```python
{
    'success': True,
    'stock_info': {
        'code': '600066',
        'target_date': '2025-06-07',
        'prev_close': 15.80,
        'board_type': '主板'
    },
    'technical_indicators': {
        'prev_hdly': 0.0856,
        'prev_j': 12.34
    },
    'price_constraints': {
        'min_price': 14.22,
        'max_price': 17.38,
        'limit_ratio': '±10%'
    },
    'prediction_results': {
        'total_valid_points': 25,
        'has_valid_range': True,
        'price_range': {
            'min': 15.20,
            'max': 16.80,
            'count': 25
        },
        'recommended_prices': {
            'lowest_valid': 15.20,
            'highest_valid': 16.80,
            'middle_point': 16.00
        }
    }
}
```

### batch_predict(stock_codes, target_date=None)

批量预测多只股票的价格范围。

**参数:**
- `stock_codes` (List[str]): 股票代码列表
- `target_date` (str, optional): 目标日期

**返回:**
- `Dict`: 批量预测结果

### calculate_price_range(stock_code, target_date, price_step=0.01, max_iterations=1000)

核心价格范围计算函数。

**参数:**
- `stock_code` (str): 股票代码
- `target_date` (str): 目标日期
- `price_step` (float): 价格步长，默认 0.01
- `max_iterations` (int): 最大迭代次数，默认 1000

## 测试

运行测试脚本验证功能：

```bash
cd predict
python test_prediction.py
```

测试包括：
- 数据加载测试
- 单只股票预测测试
- 批量预测测试
- 边界情况测试
- 性能测试

## 使用示例

### 示例 1: 基本预测

```python
from predict_main import predict_price_range

# 预测平安银行在指定日期的价格范围
result = predict_price_range('000001', '2025-06-07')

if result['success']:
    price_range = result['prediction_results']['price_range']
    print(f"预测价格范围: {price_range['min']:.2f} - {price_range['max']:.2f}")
    print(f"有效价格点数: {price_range['count']}")
else:
    print("预测失败，可能不满足技术指标条件")
```

### 示例 2: 投资决策支持

```python
from predict_main import predict_price_range

def get_investment_advice(stock_code, target_date):
    result = predict_price_range(stock_code, target_date, detailed=True)
    
    if not result['success']:
        return "不建议投资：不满足技术指标条件"
    
    stock_info = result['stock_info']
    price_range = result['prediction_results']['price_range']
    
    # 计算潜在收益
    prev_close = stock_info['prev_close']
    min_gain = (price_range['min'] - prev_close) / prev_close * 100
    max_gain = (price_range['max'] - prev_close) / prev_close * 100
    
    advice = f"""
投资建议 - {stock_code}:
- 前收盘价: {prev_close:.2f}
- 预测价格范围: {price_range['min']:.2f} - {price_range['max']:.2f}
- 潜在收益范围: {min_gain:.1f}% - {max_gain:.1f}%
- 建议关注价格: {result['prediction_results']['recommended_prices']['middle_point']:.2f}
"""
    return advice

# 使用示例
advice = get_investment_advice('600066', '2025-06-07')
print(advice)
```

## 注意事项

1. **数据依赖**: 需要确保 `data/` 目录下有对应股票的历史数据文件
2. **计算时间**: 价格范围计算可能需要几秒到几十秒，取决于价格步长和数据量
3. **技术指标**: 预测基于技术指标，不保证实际价格表现
4. **风险提示**: 本工具仅供参考，投资有风险，决策需谨慎

## 故障排除

### 常见错误

1. **"无法找到股票数据文件"**
   - 检查 `data/` 目录下是否有对应的 CSV 文件
   - 确认股票代码格式正确

2. **"历史数据不足"**
   - 确保股票数据至少有 30 个交易日
   - 检查目标日期是否在数据范围内

3. **"前一个J值不是前5天的最低点"**
   - 这是正常的技术指标条件，表示当前不满足预测条件

4. **"没有找到满足条件的价格范围"**
   - 技术指标条件较为严格，可能确实没有满足条件的价格
   - 可以尝试调整条件参数或选择其他日期

## 性能优化

- 调整 `price_step` 参数平衡精度和速度
- 使用 `detailed=False` 获取简化结果
- 批量处理时考虑并行计算

## 更新日志

- v1.0.0: 初始版本，实现基本价格范围预测功能
- 支持 hdly 和 KDJ_RSI 指标条件检查
- 支持主板/科创板涨跌幅限制
- 提供详细的预测报告和批量处理功能
