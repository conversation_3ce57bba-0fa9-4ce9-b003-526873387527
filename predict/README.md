# 股票价格范围预测模块

本模块实现了基于技术指标的股票价格范围预测功能，根据 hdly 指标和 KDJ_RSI 指标的特定条件，计算满足条件的股票价格范围。

## 功能特性

### 任务1: 单股票价格范围预测
- **技术指标计算**: 集成 hdly 和 KDJ_RSI 指标计算
- **条件检查**: 实现复杂的技术指标条件判断
- **价格范围预测**: 反推计算满足条件的价格范围
- **涨跌幅限制**: 自动识别主板/科创板并应用相应涨跌幅限制
- **详细报告**: 提供详细的预测报告和分析

### 任务2: 批量股票预测
- **全量股票分析**: 支持对data文件夹内所有股票进行批量预测
- **并行处理**: 支持多线程并行处理以提高效率
- **机会筛选**: 自动筛选和排序最佳投资机会
- **结果导出**: 支持将预测结果导出为CSV文件
- **性能优化**: 提供顺序和并行两种处理模式

## 文件结构

```
predict/
├── README.md                    # 本文档
├── plan.md                     # 原始需求说明
├── price_range_calculator.py   # 核心价格范围计算模块
├── predict_main.py             # 主要接口函数（任务1）
├── batch_predict_all.py        # 批量预测模块（任务2）
├── test_prediction.py          # 单股票预测测试脚本
├── test_batch_prediction.py    # 批量预测测试脚本
├── example_usage.py            # 单股票预测使用示例
├── batch_usage_examples.py     # 批量预测使用示例
├── batch_predict_cli.py        # 完整的命令行工具
├── run_batch_predict.py        # 简化的命令行脚本
└── batch_predict.sh            # Shell脚本包装器
```

## 预测条件

### 1. HDLY 指标条件
- 当前 hdly 指标值 > 0.1
- 当前 hdly 指标值 > 前一个指标值

### 2. KDJ_RSI J值条件
- 当前 J 值 > 前一个 J 值（上升趋势）
- 当前 J 值 < 20（超卖区域）
- 前一个 J 值是前 5 天的最低点
- 当前 J 值与前一个 J 值连线角度 > 70°

### 3. 涨跌幅限制
- 主板股票：±10%
- 科创板股票（688开头）：±20%

## 安装依赖

```bash
pip install pandas numpy talib
```

## 使用方法

### 任务1: 单股票预测

#### 基本使用

```python
from predict_main import predict_price_range

# 预测单只股票的价格范围
result = predict_price_range('600066', '2025-06-07')

if result['success']:
    print(f"有效价格范围: {result['prediction_results']['price_range']}")
else:
    print(f"预测失败: {result.get('error', '未知错误')}")
```

### 详细预测报告

```python
from predict_main import predict_price_range, print_prediction_report

# 获取详细预测结果
result = predict_price_range('600066', '2025-06-07', detailed=True)

# 打印格式化报告
if result['success']:
    print_prediction_report(result)
```

### 批量预测

```python
from predict_main import batch_predict

# 批量预测多只股票
stock_list = ['600066', '000001', '000002']
batch_result = batch_predict(stock_list, '2025-06-07')

print(f"成功率: {batch_result['success_rate']}")
```

### 自动使用最新日期

```python
# 不指定日期，自动使用下一个交易日
result = predict_price_range('600066')
```

### 任务2: 批量股票预测

#### 基本批量预测

```python
from batch_predict_all import batch_predict_all_stocks

# 批量预测所有股票
result = batch_predict_all_stocks('2025-06-07')

print(f"总股票数: {result['total_stocks']}")
print(f"发现机会: {result['successful_predictions']}")
print(f"成功率: {result['success_rate']}")
```

#### 快速机会扫描

```python
from batch_predict_all import quick_scan_opportunities

# 快速扫描高收益机会（最小收益率5%）
opportunities = quick_scan_opportunities('2025-06-07', min_return=5.0)

for opp in opportunities[:5]:
    print(f"{opp['stock_code']}: {opp['max_return_pct']:.1f}%")
```

#### 自定义批量预测

```python
from batch_predict_all import BatchPredictor

# 创建批量预测器
predictor = BatchPredictor()

# 并行预测
result = predictor.batch_predict_parallel(
    target_date='2025-06-07',
    max_workers=4  # 使用4个并行线程
)

# 获取最佳机会
top_opportunities = predictor.get_top_opportunities(
    result,
    top_n=10,
    sort_by='max_return'
)

# 保存结果到CSV
csv_file = predictor.save_results_to_csv(result)
```

### 任务2: 命令行使用

#### 使用Shell脚本（推荐）

```bash
# 显示帮助信息
./batch_predict.sh help

# 显示系统信息
./batch_predict.sh info

# 测试模式（分析前20只股票）
./batch_predict.sh test 20

# 批量预测所有股票
./batch_predict.sh all 2025-03-10

# 快速扫描投资机会（最小收益率5%）
./batch_predict.sh scan 2025-03-10 5.0
```

#### 使用Python脚本

```bash
# 简化命令行工具
python run_batch_predict.py info
python run_batch_predict.py test 20
python run_batch_predict.py all 2025-03-10
python run_batch_predict.py scan 2025-03-10 5.0

# 完整命令行工具
python batch_predict_cli.py info
python batch_predict_cli.py batch --date 2025-03-10 --limit 50 --workers 4
python batch_predict_cli.py scan --min-return 3.0 --top 5
```

#### 命令行参数说明

**批量预测参数:**
- `--date, -d`: 目标日期 (YYYY-MM-DD)
- `--workers, -w`: 并行线程数 (默认: 4)
- `--limit, -l`: 限制分析的股票数量
- `--top, -t`: 显示前N个最佳机会 (默认: 10)
- `--no-parallel`: 使用顺序处理
- `--no-csv`: 不保存CSV文件

**快速扫描参数:**
- `--min-return, -r`: 最小收益率要求 (%) (默认: 5.0)
- `--top, -t`: 显示前N个机会 (默认: 10)

## API 参考

### predict_price_range(stock_code, target_date=None, detailed=True)

预测股票价格范围的主要接口函数。

**参数:**
- `stock_code` (str): 股票代码，如 '600066'
- `target_date` (str, optional): 目标日期，格式为 'YYYY-MM-DD'
- `detailed` (bool): 是否返回详细信息，默认 True

**返回:**
- `Dict`: 预测结果字典

**返回结果结构 (detailed=True):**
```python
{
    'success': True,
    'stock_info': {
        'code': '600066',
        'target_date': '2025-06-07',
        'prev_close': 15.80,
        'board_type': '主板'
    },
    'technical_indicators': {
        'prev_hdly': 0.0856,
        'prev_j': 12.34
    },
    'price_constraints': {
        'min_price': 14.22,
        'max_price': 17.38,
        'limit_ratio': '±10%'
    },
    'prediction_results': {
        'total_valid_points': 25,
        'has_valid_range': True,
        'price_range': {
            'min': 15.20,
            'max': 16.80,
            'count': 25
        },
        'recommended_prices': {
            'lowest_valid': 15.20,
            'highest_valid': 16.80,
            'middle_point': 16.00
        }
    }
}
```

### batch_predict(stock_codes, target_date=None)

批量预测多只股票的价格范围。

**参数:**
- `stock_codes` (List[str]): 股票代码列表
- `target_date` (str, optional): 目标日期

**返回:**
- `Dict`: 批量预测结果

### calculate_price_range(stock_code, target_date, price_step=0.01, max_iterations=1000)

核心价格范围计算函数。

**参数:**
- `stock_code` (str): 股票代码
- `target_date` (str): 目标日期
- `price_step` (float): 价格步长，默认 0.01
- `max_iterations` (int): 最大迭代次数，默认 1000

## 批量预测API参考

### batch_predict_all_stocks(target_date, parallel=True, max_workers=4, save_csv=True, show_top=10)

批量预测所有股票的简化接口函数。

**参数:**
- `target_date` (str): 目标日期，格式为 'YYYY-MM-DD'
- `parallel` (bool): 是否使用并行处理，默认 True
- `max_workers` (int): 并行线程数，默认 4
- `save_csv` (bool): 是否保存结果到CSV，默认 True
- `show_top` (int): 显示前N个最佳机会，默认 10

**返回:**
- `Dict`: 批量预测结果

### quick_scan_opportunities(target_date, min_return=5.0)

快速扫描投资机会。

**参数:**
- `target_date` (str): 目标日期
- `min_return` (float): 最小收益率要求 (%)，默认 5.0

**返回:**
- `List[Dict]`: 符合条件的投资机会列表

### BatchPredictor类

批量股票预测器类，提供更灵活的批量预测功能。

#### 主要方法:

**batch_predict_parallel(target_date, max_workers=4)**
- 并行批量预测所有股票

**batch_predict_sequential(target_date)**
- 顺序批量预测所有股票

**get_top_opportunities(batch_result, top_n=10, sort_by='max_return')**
- 获取最佳投资机会

**save_results_to_csv(batch_result, filename=None)**
- 保存批量预测结果到CSV文件

## 测试

### 单股票预测测试

运行单股票预测测试脚本：

```bash
cd predict
python test_prediction.py
```

测试包括：
- 数据加载测试
- 单只股票预测测试
- 批量预测测试
- 边界情况测试
- 性能测试

### 批量预测测试

运行批量预测测试脚本：

```bash
cd predict
python test_batch_prediction.py
```

测试包括：
- 批量预测器初始化测试
- 单只股票预测测试
- 小批量顺序预测测试
- 小批量并行预测测试
- 最佳机会筛选测试
- CSV导出功能测试
- 简化接口函数测试

## 使用示例

### 示例 1: 基本预测

```python
from predict_main import predict_price_range

# 预测平安银行在指定日期的价格范围
result = predict_price_range('000001', '2025-06-07')

if result['success']:
    price_range = result['prediction_results']['price_range']
    print(f"预测价格范围: {price_range['min']:.2f} - {price_range['max']:.2f}")
    print(f"有效价格点数: {price_range['count']}")
else:
    print("预测失败，可能不满足技术指标条件")
```

### 示例 2: 投资决策支持

```python
from predict_main import predict_price_range

def get_investment_advice(stock_code, target_date):
    result = predict_price_range(stock_code, target_date, detailed=True)
    
    if not result['success']:
        return "不建议投资：不满足技术指标条件"
    
    stock_info = result['stock_info']
    price_range = result['prediction_results']['price_range']
    
    # 计算潜在收益
    prev_close = stock_info['prev_close']
    min_gain = (price_range['min'] - prev_close) / prev_close * 100
    max_gain = (price_range['max'] - prev_close) / prev_close * 100
    
    advice = f"""
投资建议 - {stock_code}:
- 前收盘价: {prev_close:.2f}
- 预测价格范围: {price_range['min']:.2f} - {price_range['max']:.2f}
- 潜在收益范围: {min_gain:.1f}% - {max_gain:.1f}%
- 建议关注价格: {result['prediction_results']['recommended_prices']['middle_point']:.2f}
"""
    return advice

# 使用示例
advice = get_investment_advice('600066', '2025-06-07')
print(advice)
```

### 示例 3: 批量投资机会扫描

```python
from batch_predict_all import batch_predict_all_stocks, quick_scan_opportunities

def scan_market_opportunities(target_date, min_return=5.0):
    """扫描市场投资机会"""
    print(f"🔍 扫描 {target_date} 的市场机会...")

    # 快速扫描高收益机会
    opportunities = quick_scan_opportunities(target_date, min_return)

    if opportunities:
        print(f"✅ 发现 {len(opportunities)} 个投资机会:")
        for i, opp in enumerate(opportunities[:5], 1):
            print(f"   {i}. {opp['stock_code']}: "
                  f"最大收益 {opp['max_return_pct']:.1f}%")

        return opportunities
    else:
        print(f"💤 未发现满足 {min_return}% 收益率的机会")
        return []

# 使用示例
opportunities = scan_market_opportunities('2025-06-07', min_return=3.0)
```

### 示例 4: 多日期批量分析

```python
from batch_predict_all import BatchPredictor
from datetime import datetime, timedelta

def analyze_multiple_dates(stock_count=50, days=5):
    """分析多个日期的投资机会"""
    predictor = BatchPredictor()

    # 只分析前N只股票
    predictor.stock_codes = predictor.stock_codes[:stock_count]

    # 生成测试日期
    base_date = datetime(2025, 3, 1)
    test_dates = []
    for i in range(days):
        test_date = base_date + timedelta(days=i*2)
        test_dates.append(test_date.strftime('%Y-%m-%d'))

    all_opportunities = {}

    for date in test_dates:
        print(f"📅 分析日期: {date}")
        result = predictor.batch_predict_parallel(date, max_workers=4)

        if result['successful_predictions'] > 0:
            opportunities = predictor.get_top_opportunities(result, top_n=3)
            all_opportunities[date] = opportunities
            print(f"   发现 {len(opportunities)} 个机会")
        else:
            print(f"   未发现机会")

    return all_opportunities

# 使用示例
multi_date_opportunities = analyze_multiple_dates(stock_count=30, days=5)
```

## 注意事项

1. **数据依赖**: 需要确保 `data/` 目录下有对应股票的历史数据文件
2. **计算时间**: 价格范围计算可能需要几秒到几十秒，取决于价格步长和数据量
3. **技术指标**: 预测基于技术指标，不保证实际价格表现
4. **风险提示**: 本工具仅供参考，投资有风险，决策需谨慎

## 故障排除

### 常见错误

1. **"无法找到股票数据文件"**
   - 检查 `data/` 目录下是否有对应的 CSV 文件
   - 确认股票代码格式正确

2. **"历史数据不足"**
   - 确保股票数据至少有 30 个交易日
   - 检查目标日期是否在数据范围内

3. **"前一个J值不是前5天的最低点"**
   - 这是正常的技术指标条件，表示当前不满足预测条件

4. **"没有找到满足条件的价格范围"**
   - 技术指标条件较为严格，可能确实没有满足条件的价格
   - 可以尝试调整条件参数或选择其他日期

## 性能优化

- 调整 `price_step` 参数平衡精度和速度
- 使用 `detailed=False` 获取简化结果
- 批量处理时考虑并行计算

## 更新日志

- v1.0.0: 初始版本，实现基本价格范围预测功能
- 支持 hdly 和 KDJ_RSI 指标条件检查
- 支持主板/科创板涨跌幅限制
- 提供详细的预测报告和批量处理功能
