#!/bin/bash

# 批量股票预测命令行工具
# 使用方法: ./batch_predict.sh <命令> [参数]

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 切换到脚本目录
cd "$SCRIPT_DIR"

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 错误: 未找到Python解释器"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 显示帮助信息
show_help() {
    echo "🚀 批量股票预测命令行工具"
    echo "============================================================"
    echo "使用方法: ./batch_predict.sh <命令> [参数]"
    echo ""
    echo "可用命令:"
    echo "  all [日期]              - 批量预测所有股票"
    echo "  scan [日期] [最小收益率] - 快速扫描投资机会"
    echo "  test [股票数量]         - 测试模式（分析前N只股票）"
    echo "  info                   - 显示系统信息"
    echo "  help                   - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./batch_predict.sh all 2025-03-10"
    echo "  ./batch_predict.sh scan 2025-03-10 5.0"
    echo "  ./batch_predict.sh test 20"
    echo "  ./batch_predict.sh info"
    echo ""
    echo "高级用法 (使用完整CLI工具):"
    echo "  ./batch_predict.sh cli batch --limit 50 --workers 4"
    echo "  ./batch_predict.sh cli scan --min-return 3.0 --top 5"
}

# 检查参数
if [ $# -eq 0 ]; then
    show_help
    exit 0
fi

COMMAND="$1"

case "$COMMAND" in
    "help" | "-h" | "--help")
        show_help
        ;;
    "all")
        echo "🚀 执行批量预测所有股票..."
        if [ $# -eq 1 ]; then
            $PYTHON_CMD run_batch_predict.py all
        else
            $PYTHON_CMD run_batch_predict.py all "$2"
        fi
        ;;
    "scan")
        echo "🔍 执行快速投资机会扫描..."
        if [ $# -eq 1 ]; then
            $PYTHON_CMD run_batch_predict.py scan
        elif [ $# -eq 2 ]; then
            $PYTHON_CMD run_batch_predict.py scan "$2"
        else
            $PYTHON_CMD run_batch_predict.py scan "$2" "$3"
        fi
        ;;
    "test")
        echo "🧪 执行测试模式..."
        if [ $# -eq 1 ]; then
            $PYTHON_CMD run_batch_predict.py test
        else
            $PYTHON_CMD run_batch_predict.py test "$2"
        fi
        ;;
    "info")
        echo "📊 显示系统信息..."
        $PYTHON_CMD run_batch_predict.py info
        ;;
    "cli")
        echo "🔧 使用完整CLI工具..."
        shift  # 移除第一个参数 "cli"
        $PYTHON_CMD batch_predict_cli.py "$@"
        ;;
    *)
        echo "❌ 未知命令: $COMMAND"
        echo ""
        show_help
        exit 1
        ;;
esac
