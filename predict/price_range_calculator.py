#!/usr/bin/env python3
"""
股票价格范围计算模块

根据给定条件反推计算满足条件的股票价格范围：
1. hdly指标条件：当前值 > 0.1 且 当前值 > 前一个值
2. KDJ_RSI J值条件：J值上升 且 J < 20 且 前一个J值是前5天最低点 且 连线角度 > 70°
3. 涨跌幅限制：主板 ±10%，科创板 ±20%
"""

import pandas as pd
import numpy as np
import sys
import os
import math
from typing import Dict, List, Tuple, Optional

# 添加父目录到路径以导入指标计算模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'hdly'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'kdj_rsi'))

from hdly.hdly_indicator import get_hdly_value
from kdj_rsi.kdj_rsi_indicator import calculate_kdj_rsi


def load_stock_data(stock_code: str) -> pd.DataFrame:
    """
    加载股票数据
    
    参数:
    stock_code: 股票代码
    
    返回:
    DataFrame: 股票数据
    """
    try:
        # 尝试从不同路径加载数据
        data_paths = [
            f"../data/{stock_code}.csv",
            f"data/{stock_code}.csv",
            f"./data/{stock_code}.csv"
        ]
        
        data = None
        for path in data_paths:
            if os.path.exists(path):
                data = pd.read_csv(path)
                break
        
        if data is None:
            raise FileNotFoundError(f"无法找到股票 {stock_code} 的数据文件")
        
        data['date'] = pd.to_datetime(data['date'])
        data = data.sort_values('date').reset_index(drop=True)
        
        return data
        
    except Exception as e:
        raise Exception(f"加载股票数据失败: {e}")


def is_kechuangban(stock_code: str) -> bool:
    """
    判断是否为科创板股票
    
    参数:
    stock_code: 股票代码
    
    返回:
    bool: True表示科创板，False表示主板
    """
    # 科创板股票代码以688开头
    return stock_code.startswith('688')


def calculate_price_limits(prev_close: float, is_kechuang: bool) -> Tuple[float, float]:
    """
    计算涨跌幅限制
    
    参数:
    prev_close: 前一日收盘价
    is_kechuang: 是否为科创板
    
    返回:
    Tuple[float, float]: (最低价格, 最高价格)
    """
    if is_kechuang:
        # 科创板 ±20%
        limit_ratio = 0.20
    else:
        # 主板 ±10%
        limit_ratio = 0.10
    
    min_price = prev_close * (1 - limit_ratio)
    max_price = prev_close * (1 + limit_ratio)
    
    return min_price, max_price


def calculate_angle(j_prev: float, j_current: float) -> float:
    """
    计算J值连线角度
    
    参数:
    j_prev: 前一个J值
    j_current: 当前J值
    
    返回:
    float: 角度（度数）
    """
    if j_current <= j_prev:
        return 0.0
    
    # 假设时间间隔为1个单位，计算角度
    delta_y = j_current - j_prev
    delta_x = 1.0  # 时间间隔
    
    # 计算角度（弧度转度数）
    angle_rad = math.atan(delta_y / delta_x)
    angle_deg = math.degrees(angle_rad)
    
    return angle_deg


def check_j_is_lowest_in_period(j_values: List[float], target_index: int, lookback_days: int = 5) -> bool:
    """
    检查目标J值是否为前N天的最低点
    
    参数:
    j_values: J值列表
    target_index: 目标索引
    lookback_days: 回看天数
    
    返回:
    bool: 是否为最低点
    """
    if target_index < lookback_days:
        return False
    
    target_j = j_values[target_index]
    start_index = target_index - lookback_days
    
    # 检查前N天的J值
    for i in range(start_index, target_index):
        if j_values[i] < target_j:
            return False
    
    return True


def simulate_hdly_with_new_data(historical_data: pd.DataFrame, target_date: str, 
                               new_low: float) -> float:
    """
    模拟计算加入新数据后的hdly指标值
    
    参数:
    historical_data: 历史数据
    target_date: 目标日期
    new_low: 新的最低价
    
    返回:
    float: 新的hdly指标值
    """
    # 创建包含新数据的DataFrame
    extended_data = historical_data.copy()
    
    # 找到目标日期的前一天数据
    target_dt = pd.to_datetime(target_date)
    prev_data = extended_data[extended_data['date'] < target_dt].iloc[-1]
    
    # 创建新的一行数据（只需要low值来计算hdly）
    new_row = prev_data.copy()
    new_row['date'] = target_dt
    new_row['low'] = new_low
    
    # 添加到数据中
    extended_data = pd.concat([extended_data, pd.DataFrame([new_row])], ignore_index=True)
    extended_data = extended_data.sort_values('date').reset_index(drop=True)
    
    # 计算hdly指标
    hdly_values = get_hdly_value(extended_data['low'])
    
    # 返回目标日期的hdly值
    target_index = len(extended_data) - 1
    return hdly_values.iloc[target_index]


def simulate_kdj_with_new_data(historical_data: pd.DataFrame, target_date: str,
                              new_high: float, new_low: float, new_close: float) -> Dict:
    """
    模拟计算加入新数据后的KDJ指标值
    
    参数:
    historical_data: 历史数据
    target_date: 目标日期
    new_high: 新的最高价
    new_low: 新的最低价
    new_close: 新的收盘价
    
    返回:
    Dict: 包含KD, J, RSI值的字典
    """
    # 创建包含新数据的DataFrame
    extended_data = historical_data.copy()
    
    # 找到目标日期的前一天数据
    target_dt = pd.to_datetime(target_date)
    prev_data = extended_data[extended_data['date'] < target_dt].iloc[-1]
    
    # 创建新的一行数据
    new_row = prev_data.copy()
    new_row['date'] = target_dt
    new_row['high'] = new_high
    new_row['low'] = new_low
    new_row['close'] = new_close
    new_row['open'] = prev_data['close']  # 假设开盘价等于前一日收盘价
    
    # 添加到数据中
    extended_data = pd.concat([extended_data, pd.DataFrame([new_row])], ignore_index=True)
    extended_data = extended_data.sort_values('date').reset_index(drop=True)
    
    # 计算KDJ_RSI指标
    kdj_rsi_data = calculate_kdj_rsi(extended_data)
    
    # 返回目标日期的指标值
    target_index = len(extended_data) - 1
    result = kdj_rsi_data.iloc[target_index]
    
    return {
        'KD': result['KD'],
        'J': result['J'],
        'RSI': result['RSI']
    }


def calculate_price_range(stock_code: str, target_date: str, 
                         price_step: float = 0.01, max_iterations: int = 1000) -> Dict:
    """
    计算满足条件的价格范围
    
    参数:
    stock_code: 股票代码
    target_date: 目标日期
    price_step: 价格步长
    max_iterations: 最大迭代次数
    
    返回:
    Dict: 计算结果
    """
    try:
        # 1. 加载股票数据
        data = load_stock_data(stock_code)
        
        # 2. 获取目标日期前的数据
        target_dt = pd.to_datetime(target_date)
        historical_data = data[data['date'] < target_dt].copy()
        
        if len(historical_data) < 30:  # 确保有足够的历史数据
            return {"error": "历史数据不足，无法计算指标"}
        
        # 3. 计算历史指标值
        hdly_values = get_hdly_value(historical_data['low'])
        kdj_rsi_data = calculate_kdj_rsi(historical_data)
        
        # 4. 获取前一天的指标值
        prev_hdly = hdly_values.iloc[-1]
        prev_j = kdj_rsi_data['J'].iloc[-1]
        
        # 5. 检查前一个J值是否为前5天最低点
        j_values = kdj_rsi_data['J'].tolist()
        if not check_j_is_lowest_in_period(j_values, len(j_values) - 1, 5):
            return {"error": "前一个J值不是前5天的最低点，不满足基础条件"}
        
        # 6. 获取前一日收盘价和涨跌幅限制
        prev_close = historical_data['close'].iloc[-1]
        is_kechuang = is_kechuangban(stock_code)
        min_price, max_price = calculate_price_limits(prev_close, is_kechuang)
        
        # 7. 搜索满足条件的价格范围
        valid_ranges = []
        
        # 遍历可能的价格范围
        current_price = min_price
        while current_price <= max_price and len(valid_ranges) < max_iterations:
            # 假设high = low = close = current_price（简化情况）
            # 在实际应用中，可以设置不同的high, low, close组合
            
            # 检查hdly条件
            new_hdly = simulate_hdly_with_new_data(historical_data, target_date, current_price)
            hdly_condition = new_hdly > 0.1 and new_hdly > prev_hdly
            
            # 检查KDJ条件
            kdj_result = simulate_kdj_with_new_data(historical_data, target_date, 
                                                  current_price, current_price, current_price)
            new_j = kdj_result['J']
            
            # J值条件检查
            j_increased = new_j > prev_j
            j_below_20 = new_j < 20
            angle = calculate_angle(prev_j, new_j)
            angle_condition = angle > 70
            
            kdj_condition = j_increased and j_below_20 and angle_condition
            
            # 如果所有条件都满足
            if hdly_condition and kdj_condition:
                valid_ranges.append({
                    'price': current_price,
                    'hdly_value': new_hdly,
                    'j_value': new_j,
                    'angle': angle
                })
            
            current_price += price_step
        
        # 8. 返回结果
        result = {
            'stock_code': stock_code,
            'target_date': target_date,
            'prev_close': prev_close,
            'prev_hdly': prev_hdly,
            'prev_j': prev_j,
            'price_limits': {
                'min': min_price,
                'max': max_price,
                'is_kechuangban': is_kechuang
            },
            'valid_price_ranges': valid_ranges,
            'conditions_summary': {
                'hdly_condition': f"hdly > 0.1 且 hdly > {prev_hdly:.4f}",
                'j_condition': f"J > {prev_j:.2f} 且 J < 20 且 角度 > 70°",
                'price_limit': f"价格在 {min_price:.2f} - {max_price:.2f} 范围内"
            }
        }
        
        return result
        
    except Exception as e:
        return {"error": f"计算价格范围失败: {e}"}


if __name__ == "__main__":
    # 测试代码
    stock_code = "600066"
    target_date = "2025-06-07"  # 示例日期
    
    print(f"计算股票 {stock_code} 在 {target_date} 的价格范围...")
    print("=" * 60)
    
    result = calculate_price_range(stock_code, target_date)
    
    if "error" in result:
        print(f"❌ 错误: {result['error']}")
    else:
        print(f"📊 股票代码: {result['stock_code']}")
        print(f"📅 目标日期: {result['target_date']}")
        print(f"💰 前一日收盘价: {result['prev_close']:.2f}")
        print(f"📈 前一日hdly值: {result['prev_hdly']:.4f}")
        print(f"📊 前一日J值: {result['prev_j']:.2f}")
        print()
        
        print("💡 条件说明:")
        for key, condition in result['conditions_summary'].items():
            print(f"  - {condition}")
        print()
        
        print(f"📏 价格限制范围: {result['price_limits']['min']:.2f} - {result['price_limits']['max']:.2f}")
        print(f"🏢 板块类型: {'科创板' if result['price_limits']['is_kechuangban'] else '主板'}")
        print()
        
        if result['valid_price_ranges']:
            print(f"✅ 找到 {len(result['valid_price_ranges'])} 个满足条件的价格点:")
            for i, price_info in enumerate(result['valid_price_ranges'][:10]):  # 只显示前10个
                print(f"  {i+1}. 价格: {price_info['price']:.2f}, "
                      f"hdly: {price_info['hdly_value']:.4f}, "
                      f"J值: {price_info['j_value']:.2f}, "
                      f"角度: {price_info['angle']:.1f}°")
            
            if len(result['valid_price_ranges']) > 10:
                print(f"  ... 还有 {len(result['valid_price_ranges']) - 10} 个价格点")
        else:
            print("❌ 没有找到满足所有条件的价格范围")
