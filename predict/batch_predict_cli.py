#!/usr/bin/env python3
"""
批量股票预测命令行工具

提供命令行接口来执行批量股票预测功能
"""

import sys
import os
import argparse
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from batch_predict_all import (
    BatchPredictor, 
    batch_predict_all_stocks, 
    quick_scan_opportunities
)


def get_next_trading_date():
    """获取下一个交易日（简单实现，跳过周末）"""
    today = datetime.now()
    next_date = today + timedelta(days=1)
    
    # 跳过周末
    while next_date.weekday() >= 5:  # 5=Saturday, 6=Sunday
        next_date += timedelta(days=1)
    
    return next_date.strftime('%Y-%m-%d')


def cmd_batch_predict(args):
    """执行批量预测命令"""
    print(f"🚀 批量股票预测")
    print("="*60)
    
    target_date = args.date or get_next_trading_date()
    
    print(f"📅 目标日期: {target_date}")
    print(f"🔧 并行线程: {args.workers}")
    print(f"📊 显示前 {args.top} 个机会")
    
    if args.limit:
        print(f"⚠️  限制分析前 {args.limit} 只股票")
        # 创建自定义预测器
        predictor = BatchPredictor()
        predictor.stock_codes = predictor.stock_codes[:args.limit]
        
        if args.parallel:
            result = predictor.batch_predict_parallel(target_date, args.workers)
        else:
            result = predictor.batch_predict_sequential(target_date)
        
        # 手动处理结果显示
        from batch_predict_all import print_batch_summary
        print_batch_summary(result)
        
        if result['successful_predictions'] > 0:
            print(f"\n✨ 前 {args.top} 个最佳投资机会:")
            top_opportunities = predictor.get_top_opportunities(
                result, top_n=args.top, sort_by='max_return'
            )
            
            for i, opp in enumerate(top_opportunities, 1):
                print(f"   {i:2d}. {opp['stock_code']} ({opp['board_type']}): "
                      f"价格 {opp['price_range']['min']:.2f}-{opp['price_range']['max']:.2f}, "
                      f"收益 {opp['potential_return']['min_pct']:.1f}%-{opp['potential_return']['max_pct']:.1f}%")
        
        if args.save_csv:
            csv_file = predictor.save_results_to_csv(result)
            print(f"\n💾 详细结果已保存到: {csv_file}")
    else:
        # 使用简化接口
        result = batch_predict_all_stocks(
            target_date=target_date,
            parallel=args.parallel,
            max_workers=args.workers,
            save_csv=args.save_csv,
            show_top=args.top
        )
    
    return result


def cmd_quick_scan(args):
    """执行快速扫描命令"""
    print(f"🔍 快速投资机会扫描")
    print("="*60)
    
    target_date = args.date or get_next_trading_date()
    
    print(f"📅 目标日期: {target_date}")
    print(f"💰 最小收益率: {args.min_return}%")
    
    if args.limit:
        print(f"⚠️  限制分析前 {args.limit} 只股票")
        # 创建自定义预测器进行限制
        predictor = BatchPredictor()
        original_codes = predictor.stock_codes
        predictor.stock_codes = predictor.stock_codes[:args.limit]
        
        # 执行批量预测
        batch_result = predictor.batch_predict_parallel(target_date, args.workers)
        
        # 筛选符合收益率要求的机会
        opportunities = []
        for stock_code, result in batch_result['results'].items():
            if (result['success'] and 
                'potential_return' in result and 
                result['potential_return']['max_pct'] >= args.min_return):
                
                opportunities.append({
                    'stock_code': stock_code,
                    'max_return_pct': result['potential_return']['max_pct'],
                    'price_range': result['price_range'],
                    'board_type': result['board_type']
                })
        
        # 按收益率排序
        opportunities.sort(key=lambda x: x['max_return_pct'], reverse=True)
        
        print(f"\n✅ 发现 {len(opportunities)} 个符合条件的投资机会:")
        for i, opp in enumerate(opportunities[:args.top], 1):
            print(f"   {i}. {opp['stock_code']}: {opp['max_return_pct']:.1f}% "
                  f"({opp['price_range']['min']:.2f}-{opp['price_range']['max']:.2f})")
        
        return opportunities
    else:
        # 使用简化接口
        opportunities = quick_scan_opportunities(target_date, args.min_return)
        return opportunities


def cmd_info(args):
    """显示系统信息"""
    print(f"📊 批量预测系统信息")
    print("="*60)
    
    predictor = BatchPredictor()
    
    print(f"📁 数据目录: {predictor.data_dir}")
    print(f"📈 发现股票数: {len(predictor.stock_codes)}")
    print(f"📋 股票列表: {predictor.stock_codes[:20]}...")
    
    if len(predictor.stock_codes) > 20:
        print(f"   ... 还有 {len(predictor.stock_codes) - 20} 只股票")
    
    # 检查数据文件状态
    import glob
    csv_files = glob.glob(os.path.join(predictor.data_dir, "*.csv"))
    print(f"📄 CSV文件数: {len(csv_files)}")
    
    # 显示一些示例股票代码
    print(f"\n📋 股票代码示例:")
    for i, code in enumerate(predictor.stock_codes[:10], 1):
        print(f"   {i:2d}. {code}")


def main():
    """主函数 - 命令行参数解析"""
    parser = argparse.ArgumentParser(
        description="批量股票预测命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 批量预测所有股票
  python batch_predict_cli.py batch --date 2025-03-10

  # 快速扫描高收益机会
  python batch_predict_cli.py scan --min-return 5.0

  # 限制分析前50只股票
  python batch_predict_cli.py batch --limit 50 --workers 4

  # 顺序处理（不使用并行）
  python batch_predict_cli.py batch --no-parallel

  # 显示系统信息
  python batch_predict_cli.py info
        """
    )
    
    # 创建子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 批量预测命令
    batch_parser = subparsers.add_parser('batch', help='执行批量股票预测')
    batch_parser.add_argument('--date', '-d', type=str, 
                             help='目标日期 (YYYY-MM-DD)，默认为下一个交易日')
    batch_parser.add_argument('--workers', '-w', type=int, default=4,
                             help='并行线程数 (默认: 4)')
    batch_parser.add_argument('--top', '-t', type=int, default=10,
                             help='显示前N个最佳机会 (默认: 10)')
    batch_parser.add_argument('--limit', '-l', type=int,
                             help='限制分析的股票数量')
    batch_parser.add_argument('--no-parallel', action='store_false', dest='parallel',
                             help='使用顺序处理（不使用并行）')
    batch_parser.add_argument('--no-csv', action='store_false', dest='save_csv',
                             help='不保存CSV文件')
    batch_parser.set_defaults(func=cmd_batch_predict, parallel=True, save_csv=True)
    
    # 快速扫描命令
    scan_parser = subparsers.add_parser('scan', help='快速扫描投资机会')
    scan_parser.add_argument('--date', '-d', type=str,
                            help='目标日期 (YYYY-MM-DD)，默认为下一个交易日')
    scan_parser.add_argument('--min-return', '-r', type=float, default=5.0,
                            help='最小收益率要求 (%%) (默认: 5.0)')
    scan_parser.add_argument('--top', '-t', type=int, default=10,
                            help='显示前N个机会 (默认: 10)')
    scan_parser.add_argument('--limit', '-l', type=int,
                            help='限制分析的股票数量')
    scan_parser.add_argument('--workers', '-w', type=int, default=4,
                            help='并行线程数 (默认: 4)')
    scan_parser.set_defaults(func=cmd_quick_scan)
    
    # 信息命令
    info_parser = subparsers.add_parser('info', help='显示系统信息')
    info_parser.set_defaults(func=cmd_info)
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        # 执行对应的命令函数
        result = args.func(args)
        
        print(f"\n🎉 命令执行完成！")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        import traceback
        if '--debug' in sys.argv:
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
