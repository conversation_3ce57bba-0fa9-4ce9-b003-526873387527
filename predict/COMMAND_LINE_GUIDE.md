# 批量股票预测命令行使用指南

本指南介绍如何使用命令行工具进行批量股票预测。

## 🚀 快速开始

### 1. 最简单的使用方式 - Shell脚本

```bash
# 给脚本添加执行权限（首次使用）
chmod +x batch_predict.sh

# 显示帮助信息
./batch_predict.sh help

# 显示系统信息
./batch_predict.sh info

# 测试模式（分析前10只股票）
./batch_predict.sh test 10

# 批量预测所有股票
./batch_predict.sh all 2025-03-10

# 快速扫描投资机会（最小收益率5%）
./batch_predict.sh scan 2025-03-10 5.0
```

### 2. Python脚本方式

```bash
# 显示帮助
python run_batch_predict.py

# 系统信息
python run_batch_predict.py info

# 测试模式
python run_batch_predict.py test 20

# 批量预测
python run_batch_predict.py all 2025-03-10

# 快速扫描
python run_batch_predict.py scan 2025-03-10 5.0
```

### 3. 完整CLI工具

```bash
# 显示帮助
python batch_predict_cli.py --help

# 批量预测命令
python batch_predict_cli.py batch --help
python batch_predict_cli.py batch --date 2025-03-10 --limit 50 --workers 4

# 快速扫描命令
python batch_predict_cli.py scan --help
python batch_predict_cli.py scan --min-return 3.0 --top 5

# 系统信息
python batch_predict_cli.py info
```

## 📋 命令详解

### Shell脚本命令 (`./batch_predict.sh`)

| 命令 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `help` | 无 | 显示帮助信息 | `./batch_predict.sh help` |
| `info` | 无 | 显示系统信息 | `./batch_predict.sh info` |
| `test` | `[股票数量]` | 测试模式，分析前N只股票 | `./batch_predict.sh test 20` |
| `all` | `[日期]` | 批量预测所有股票 | `./batch_predict.sh all 2025-03-10` |
| `scan` | `[日期] [最小收益率]` | 快速扫描投资机会 | `./batch_predict.sh scan 2025-03-10 5.0` |
| `cli` | `[CLI参数]` | 使用完整CLI工具 | `./batch_predict.sh cli batch --limit 50` |

### Python脚本命令 (`python run_batch_predict.py`)

| 命令 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `info` | 无 | 显示系统信息 | `python run_batch_predict.py info` |
| `test` | `[股票数量]` | 测试模式 | `python run_batch_predict.py test 20` |
| `all` | `[日期]` | 批量预测所有股票 | `python run_batch_predict.py all 2025-03-10` |
| `scan` | `[日期] [最小收益率]` | 快速扫描 | `python run_batch_predict.py scan 2025-03-10 5.0` |

### 完整CLI工具 (`python batch_predict_cli.py`)

#### 批量预测命令 (`batch`)

```bash
python batch_predict_cli.py batch [选项]
```

**选项:**
- `--date, -d`: 目标日期 (YYYY-MM-DD)，默认为下一个交易日
- `--workers, -w`: 并行线程数，默认4
- `--top, -t`: 显示前N个最佳机会，默认10
- `--limit, -l`: 限制分析的股票数量
- `--no-parallel`: 使用顺序处理（不使用并行）
- `--no-csv`: 不保存CSV文件

**示例:**
```bash
# 基本批量预测
python batch_predict_cli.py batch --date 2025-03-10

# 限制分析前100只股票，使用8个线程
python batch_predict_cli.py batch --limit 100 --workers 8

# 顺序处理，不保存CSV
python batch_predict_cli.py batch --no-parallel --no-csv
```

#### 快速扫描命令 (`scan`)

```bash
python batch_predict_cli.py scan [选项]
```

**选项:**
- `--date, -d`: 目标日期 (YYYY-MM-DD)
- `--min-return, -r`: 最小收益率要求 (%)，默认5.0
- `--top, -t`: 显示前N个机会，默认10
- `--limit, -l`: 限制分析的股票数量
- `--workers, -w`: 并行线程数，默认4

**示例:**
```bash
# 扫描收益率大于3%的机会
python batch_predict_cli.py scan --min-return 3.0

# 限制分析前200只股票，显示前20个机会
python batch_predict_cli.py scan --limit 200 --top 20
```

## 🎯 使用场景

### 1. 日常快速检查

```bash
# 快速查看系统状态
./batch_predict.sh info

# 测试少量股票
./batch_predict.sh test 10
```

### 2. 投资机会扫描

```bash
# 扫描今日高收益机会
./batch_predict.sh scan

# 扫描特定日期的机会
./batch_predict.sh scan 2025-03-15 5.0

# 扫描低门槛机会
./batch_predict.sh scan 2025-03-15 2.0
```

### 3. 全面分析

```bash
# 分析所有股票（需要较长时间）
./batch_predict.sh all 2025-03-10

# 限制分析范围以节省时间
python batch_predict_cli.py batch --limit 200 --workers 8
```

### 4. 自动化脚本

```bash
#!/bin/bash
# 每日投资机会扫描脚本

DATE=$(date +%Y-%m-%d)
echo "扫描 $DATE 的投资机会..."

# 扫描高收益机会
./batch_predict.sh scan $DATE 5.0 > daily_opportunities.txt

# 发送结果邮件或其他处理
# ...
```

## 📊 输出说明

### 系统信息输出

```
📊 系统信息
📁 数据目录: ../data
📈 发现股票数: 653
📋 前20只股票: ['000001', '000002', ...]
📄 CSV文件数: 653

📊 股票代码分布:
   000xxx: 97 只
   600xxx: 219 只
   ...
```

### 批量预测结果

```
📊 批量股票预测结果摘要
📅 目标日期: 2025-03-10
📈 总股票数: 20
✅ 成功预测: 3
🎯 成功率: 15.0%
⏱️  总耗时: 16.40 秒
📊 平均耗时: 0.820 秒/股
🔧 并行线程: 2

✨ 最佳投资机会:
   1. 600066 (主板): 收益 4.2%, 价格 25.50-26.00
   2. 000001 (主板): 收益 3.8%, 价格 15.30-15.80
   ...

💾 详细结果已保存到: batch_result_20250310_143052.csv
```

## ⚠️ 注意事项

1. **性能考虑**
   - 全量分析653只股票需要较长时间（约10-15分钟）
   - 建议先使用测试模式或限制股票数量
   - 并行处理可以显著提高速度

2. **资源使用**
   - 默认使用4个并行线程
   - 可根据机器性能调整线程数
   - 内存使用量与分析的股票数量成正比

3. **结果解读**
   - 技术指标条件较为严格，成功率通常较低
   - 没有发现机会是正常现象
   - 建议结合多个日期的分析结果

4. **数据要求**
   - 确保data文件夹内有足够的历史数据
   - 股票数据文件格式需要正确
   - 目标日期不能超出数据范围

## 🔧 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod +x batch_predict.sh
   ```

2. **Python路径问题**
   ```bash
   # 使用完整路径
   /usr/bin/python3 run_batch_predict.py info
   ```

3. **数据文件问题**
   ```bash
   # 检查数据目录
   ls -la ../data/*.csv | head -10
   ```

4. **内存不足**
   ```bash
   # 减少并行线程数
   python batch_predict_cli.py batch --workers 2
   
   # 限制分析股票数量
   python batch_predict_cli.py batch --limit 100
   ```

## 📝 最佳实践

1. **首次使用**
   - 先运行 `./batch_predict.sh info` 检查系统状态
   - 使用 `./batch_predict.sh test 10` 进行小规模测试

2. **日常使用**
   - 使用 `./batch_predict.sh scan` 快速扫描机会
   - 根据需要调整最小收益率要求

3. **深度分析**
   - 使用完整CLI工具进行精细控制
   - 保存结果到CSV文件进行后续分析

4. **自动化**
   - 编写脚本定期执行扫描
   - 结合其他工具进行结果处理和通知
