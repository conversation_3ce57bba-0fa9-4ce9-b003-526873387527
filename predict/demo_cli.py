#!/usr/bin/env python3
"""
命令行工具演示脚本

演示批量预测的命令行使用方法
"""

import subprocess
import sys
import os
import time

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"💻 命令: {cmd}")
    print(f"{'='*60}")
    
    try:
        # 运行命令
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            print(result.stdout)
            if result.stderr:
                print(f"⚠️  警告: {result.stderr}")
        else:
            print(f"❌ 命令执行失败 (返回码: {result.returncode})")
            print(f"错误输出: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 执行异常: {e}")


def main():
    """主演示函数"""
    print("🚀 批量股票预测命令行工具演示")
    print("="*80)
    print("本演示将展示如何使用命令行工具进行批量股票预测")
    print("="*80)
    
    # 检查文件是否存在
    script_dir = os.path.dirname(__file__)
    shell_script = os.path.join(script_dir, "batch_predict.sh")
    python_script = os.path.join(script_dir, "run_batch_predict.py")
    cli_script = os.path.join(script_dir, "batch_predict_cli.py")
    
    print(f"📁 脚本目录: {script_dir}")
    print(f"📄 Shell脚本: {'✅' if os.path.exists(shell_script) else '❌'} {shell_script}")
    print(f"📄 Python脚本: {'✅' if os.path.exists(python_script) else '❌'} {python_script}")
    print(f"📄 CLI工具: {'✅' if os.path.exists(cli_script) else '❌'} {cli_script}")
    
    # 演示1: 显示帮助信息
    run_command("./batch_predict.sh help", "显示Shell脚本帮助信息")
    
    # 演示2: 显示系统信息
    run_command("./batch_predict.sh info", "显示系统信息")
    
    # 演示3: 测试模式
    run_command("./batch_predict.sh test 5", "测试模式（分析前5只股票）")
    
    # 演示4: Python脚本帮助
    run_command("python run_batch_predict.py", "Python脚本帮助信息")
    
    # 演示5: 完整CLI工具帮助
    run_command("python batch_predict_cli.py --help", "完整CLI工具帮助信息")
    
    # 演示6: CLI工具子命令帮助
    run_command("python batch_predict_cli.py batch --help", "批量预测命令帮助")
    
    # 演示7: CLI工具信息命令
    run_command("python batch_predict_cli.py info", "CLI工具显示系统信息")
    
    print(f"\n{'='*80}")
    print("🎉 命令行工具演示完成！")
    print("="*80)
    
    print(f"\n💡 使用总结:")
    print(f"1. 🐚 Shell脚本 (最简单):")
    print(f"   ./batch_predict.sh help")
    print(f"   ./batch_predict.sh info")
    print(f"   ./batch_predict.sh test 20")
    print(f"   ./batch_predict.sh all 2025-03-10")
    print(f"   ./batch_predict.sh scan 2025-03-10 5.0")
    
    print(f"\n2. 🐍 Python脚本 (中等复杂度):")
    print(f"   python run_batch_predict.py info")
    print(f"   python run_batch_predict.py test 20")
    print(f"   python run_batch_predict.py all 2025-03-10")
    print(f"   python run_batch_predict.py scan 2025-03-10 5.0")
    
    print(f"\n3. ⚙️  完整CLI工具 (最灵活):")
    print(f"   python batch_predict_cli.py info")
    print(f"   python batch_predict_cli.py batch --date 2025-03-10 --limit 50")
    print(f"   python batch_predict_cli.py scan --min-return 3.0 --top 5")
    
    print(f"\n🚀 推荐使用方式:")
    print(f"   - 日常使用: ./batch_predict.sh")
    print(f"   - 自动化脚本: python run_batch_predict.py")
    print(f"   - 高级配置: python batch_predict_cli.py")
    
    print(f"\n📝 实际使用示例:")
    print(f"   # 快速测试")
    print(f"   ./batch_predict.sh test 10")
    print(f"   ")
    print(f"   # 扫描今日机会")
    print(f"   ./batch_predict.sh scan")
    print(f"   ")
    print(f"   # 分析特定日期")
    print(f"   ./batch_predict.sh all 2025-03-15")
    print(f"   ")
    print(f"   # 高收益机会扫描")
    print(f"   ./batch_predict.sh scan 2025-03-15 8.0")


if __name__ == "__main__":
    main()
