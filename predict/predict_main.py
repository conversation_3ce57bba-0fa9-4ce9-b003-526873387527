#!/usr/bin/env python3
"""
股票价格预测主模块

提供简单易用的接口来预测满足特定技术指标条件的股票价格范围
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Optional, List
import pandas as pd

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from price_range_calculator import calculate_price_range, load_stock_data


def get_latest_trading_date(stock_code: str) -> Optional[str]:
    """
    获取股票的最新交易日期
    
    参数:
    stock_code: 股票代码
    
    返回:
    str: 最新交易日期，格式为 YYYY-MM-DD
    """
    try:
        data = load_stock_data(stock_code)
        latest_date = data['date'].max()
        return latest_date.strftime('%Y-%m-%d')
    except Exception as e:
        print(f"获取最新交易日期失败: {e}")
        return None


def predict_price_range(stock_code: str, target_date: Optional[str] = None, 
                       detailed: bool = True) -> Dict:
    """
    预测股票价格范围的主要接口函数
    
    参数:
    stock_code: 股票代码，如 '600066'
    target_date: 目标日期，格式为 'YYYY-MM-DD'，如果为None则使用下一个交易日
    detailed: 是否返回详细信息
    
    返回:
    Dict: 预测结果
    """
    print(f"🔍 开始预测股票 {stock_code} 的价格范围...")
    
    # 如果没有指定目标日期，使用下一个交易日
    if target_date is None:
        latest_date = get_latest_trading_date(stock_code)
        if latest_date is None:
            return {"error": "无法获取股票数据"}
        
        # 假设下一个交易日（简化处理，实际应考虑节假日）
        latest_dt = datetime.strptime(latest_date, '%Y-%m-%d')
        next_dt = latest_dt + timedelta(days=1)
        target_date = next_dt.strftime('%Y-%m-%d')
        print(f"📅 使用目标日期: {target_date}")
    
    # 调用价格范围计算函数
    result = calculate_price_range(stock_code, target_date)
    
    if "error" in result:
        return result
    
    # 处理结果
    if detailed:
        return format_detailed_result(result)
    else:
        return format_simple_result(result)


def format_detailed_result(result: Dict) -> Dict:
    """
    格式化详细结果
    
    参数:
    result: 原始计算结果
    
    返回:
    Dict: 格式化后的详细结果
    """
    valid_ranges = result['valid_price_ranges']
    
    formatted_result = {
        'success': True,
        'stock_info': {
            'code': result['stock_code'],
            'target_date': result['target_date'],
            'prev_close': result['prev_close'],
            'board_type': '科创板' if result['price_limits']['is_kechuangban'] else '主板'
        },
        'technical_indicators': {
            'prev_hdly': result['prev_hdly'],
            'prev_j': result['prev_j']
        },
        'price_constraints': {
            'min_price': result['price_limits']['min'],
            'max_price': result['price_limits']['max'],
            'limit_ratio': '±20%' if result['price_limits']['is_kechuangban'] else '±10%'
        },
        'conditions': result['conditions_summary'],
        'prediction_results': {
            'total_valid_points': len(valid_ranges),
            'has_valid_range': len(valid_ranges) > 0
        }
    }
    
    if valid_ranges:
        # 计算价格范围统计
        prices = [p['price'] for p in valid_ranges]
        formatted_result['prediction_results'].update({
            'price_range': {
                'min': min(prices),
                'max': max(prices),
                'count': len(prices)
            },
            'sample_points': valid_ranges[:5],  # 前5个样本点
            'recommended_prices': {
                'lowest_valid': min(prices),
                'highest_valid': max(prices),
                'middle_point': (min(prices) + max(prices)) / 2
            }
        })
    
    return formatted_result


def format_simple_result(result: Dict) -> Dict:
    """
    格式化简单结果
    
    参数:
    result: 原始计算结果
    
    返回:
    Dict: 格式化后的简单结果
    """
    valid_ranges = result['valid_price_ranges']
    
    if not valid_ranges:
        return {
            'success': False,
            'message': '没有找到满足条件的价格范围',
            'stock_code': result['stock_code'],
            'target_date': result['target_date']
        }
    
    prices = [p['price'] for p in valid_ranges]
    
    return {
        'success': True,
        'stock_code': result['stock_code'],
        'target_date': result['target_date'],
        'valid_price_range': {
            'min': min(prices),
            'max': max(prices),
            'count': len(prices)
        },
        'recommendation': {
            'buy_price_range': f"{min(prices):.2f} - {max(prices):.2f}",
            'prev_close': result['prev_close']
        }
    }


def batch_predict(stock_codes: List[str], target_date: Optional[str] = None) -> Dict:
    """
    批量预测多只股票的价格范围
    
    参数:
    stock_codes: 股票代码列表
    target_date: 目标日期
    
    返回:
    Dict: 批量预测结果
    """
    print(f"🔄 开始批量预测 {len(stock_codes)} 只股票...")
    
    results = {}
    successful_predictions = 0
    
    for i, stock_code in enumerate(stock_codes, 1):
        print(f"\n📊 处理第 {i}/{len(stock_codes)} 只股票: {stock_code}")
        
        result = predict_price_range(stock_code, target_date, detailed=False)
        results[stock_code] = result
        
        if result.get('success', False):
            successful_predictions += 1
            print(f"✅ {stock_code}: 找到有效价格范围")
        else:
            print(f"❌ {stock_code}: {result.get('message', '预测失败')}")
    
    summary = {
        'total_stocks': len(stock_codes),
        'successful_predictions': successful_predictions,
        'success_rate': f"{successful_predictions/len(stock_codes)*100:.1f}%",
        'results': results
    }
    
    print(f"\n📈 批量预测完成:")
    print(f"   总股票数: {summary['total_stocks']}")
    print(f"   成功预测: {summary['successful_predictions']}")
    print(f"   成功率: {summary['success_rate']}")
    
    return summary


def print_prediction_report(result: Dict):
    """
    打印预测报告
    
    参数:
    result: 预测结果
    """
    if not result.get('success', False):
        print(f"❌ 预测失败: {result.get('message', '未知错误')}")
        return
    
    print("\n" + "="*60)
    print("📊 股票价格范围预测报告")
    print("="*60)
    
    stock_info = result['stock_info']
    print(f"📈 股票代码: {stock_info['code']}")
    print(f"📅 目标日期: {stock_info['target_date']}")
    print(f"💰 前收盘价: {stock_info['prev_close']:.2f}")
    print(f"🏢 板块类型: {stock_info['board_type']}")
    
    print(f"\n📊 技术指标:")
    tech = result['technical_indicators']
    print(f"   HDLY前值: {tech['prev_hdly']:.4f}")
    print(f"   J值前值: {tech['prev_j']:.2f}")
    
    print(f"\n📏 价格约束:")
    constraints = result['price_constraints']
    print(f"   涨跌幅限制: {constraints['limit_ratio']}")
    print(f"   价格区间: {constraints['min_price']:.2f} - {constraints['max_price']:.2f}")
    
    prediction = result['prediction_results']
    if prediction['has_valid_range']:
        print(f"\n✅ 预测结果:")
        price_range = prediction['price_range']
        print(f"   有效价格点数: {price_range['count']}")
        print(f"   价格范围: {price_range['min']:.2f} - {price_range['max']:.2f}")
        
        recommended = prediction['recommended_prices']
        print(f"\n💡 建议价格:")
        print(f"   最低有效价: {recommended['lowest_valid']:.2f}")
        print(f"   最高有效价: {recommended['highest_valid']:.2f}")
        print(f"   中位价格: {recommended['middle_point']:.2f}")
        
        print(f"\n📋 样本价格点:")
        for i, point in enumerate(prediction['sample_points'], 1):
            print(f"   {i}. 价格: {point['price']:.2f}, "
                  f"HDLY: {point['hdly_value']:.4f}, "
                  f"J值: {point['j_value']:.2f}, "
                  f"角度: {point['angle']:.1f}°")
    else:
        print(f"\n❌ 没有找到满足条件的价格范围")
    
    print("="*60)


if __name__ == "__main__":
    # 示例使用
    stock_code = "600012"
    
    # 单只股票预测
    print("🎯 单只股票价格范围预测示例")
    result = predict_price_range(stock_code, detailed=True)
    
    if result.get('success', False):
        print_prediction_report(result)
    else:
        print(f"❌ 预测失败: {result.get('error', '未知错误')}")
    
    print("\n" + "="*60)
    
    # 批量预测示例
    print("🎯 批量股票预测示例")
    stock_list = ["600066", "000001", "000002"]
    batch_result = batch_predict(stock_list)
    
    # 显示有成功预测的股票
    print(f"\n📋 成功预测的股票:")
    for code, res in batch_result['results'].items():
        if res.get('success', False):
            range_info = res['valid_price_range']
            print(f"   {code}: {range_info['min']:.2f} - {range_info['max']:.2f} "
                  f"({range_info['count']} 个有效点)")
