#!/usr/bin/env python3
"""
简化的批量预测执行脚本

提供简单的命令行接口来快速执行批量预测
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from batch_predict_all import batch_predict_all_stocks, quick_scan_opportunities, BatchPredictor


def get_next_trading_date():
    """获取下一个交易日（简单实现，跳过周末）"""
    today = datetime.now()
    next_date = today + timedelta(days=1)
    
    # 跳过周末
    while next_date.weekday() >= 5:  # 5=Saturday, 6=Sunday
        next_date += timedelta(days=1)
    
    return next_date.strftime('%Y-%m-%d')


def main():
    """主函数"""
    print("🚀 批量股票预测快速执行工具")
    print("="*60)
    
    # 解析命令行参数
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python run_batch_predict.py <命令> [参数]")
        print("")
        print("可用命令:")
        print("  all [日期]              - 批量预测所有股票")
        print("  scan [日期] [最小收益率] - 快速扫描投资机会")
        print("  test [股票数量]         - 测试模式（分析前N只股票）")
        print("  info                   - 显示系统信息")
        print("")
        print("示例:")
        print("  python run_batch_predict.py all 2025-03-10")
        print("  python run_batch_predict.py scan 2025-03-10 5.0")
        print("  python run_batch_predict.py test 20")
        print("  python run_batch_predict.py info")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == "all":
            # 批量预测所有股票
            target_date = sys.argv[2] if len(sys.argv) > 2 else get_next_trading_date()
            
            print(f"📊 批量预测所有股票")
            print(f"📅 目标日期: {target_date}")
            print(f"⚠️  注意: 这将分析所有653只股票，可能需要较长时间...")
            
            # 询问用户确认
            confirm = input("是否继续？(y/N): ").strip().lower()
            if confirm not in ['y', 'yes', '是']:
                print("❌ 用户取消操作")
                return
            
            result = batch_predict_all_stocks(
                target_date=target_date,
                parallel=True,
                max_workers=4,
                save_csv=True,
                show_top=10
            )
            
        elif command == "scan":
            # 快速扫描投资机会
            target_date = sys.argv[2] if len(sys.argv) > 2 else get_next_trading_date()
            min_return = float(sys.argv[3]) if len(sys.argv) > 3 else 5.0
            
            print(f"🔍 快速扫描投资机会")
            print(f"📅 目标日期: {target_date}")
            print(f"💰 最小收益率: {min_return}%")
            print(f"⚠️  注意: 这将分析所有653只股票...")
            
            opportunities = quick_scan_opportunities(target_date, min_return)
            
        elif command == "test":
            # 测试模式
            stock_count = int(sys.argv[2]) if len(sys.argv) > 2 else 20
            target_date = get_next_trading_date()
            
            print(f"🧪 测试模式")
            print(f"📊 分析前 {stock_count} 只股票")
            print(f"📅 目标日期: {target_date}")
            
            # 创建自定义预测器
            predictor = BatchPredictor()
            predictor.stock_codes = predictor.stock_codes[:stock_count]
            
            result = predictor.batch_predict_parallel(target_date, max_workers=2)
            
            # 显示结果
            from batch_predict_all import print_batch_summary
            print_batch_summary(result)
            
            if result['successful_predictions'] > 0:
                print(f"\n✨ 最佳投资机会:")
                top_opportunities = predictor.get_top_opportunities(
                    result, top_n=5, sort_by='max_return'
                )
                
                for i, opp in enumerate(top_opportunities, 1):
                    print(f"   {i}. {opp['stock_code']}: "
                          f"收益 {opp['potential_return']['max_pct']:.1f}%, "
                          f"价格 {opp['price_range']['min']:.2f}-{opp['price_range']['max']:.2f}")
            
        elif command == "info":
            # 显示系统信息
            predictor = BatchPredictor()
            
            print(f"📊 系统信息")
            print(f"📁 数据目录: {predictor.data_dir}")
            print(f"📈 发现股票数: {len(predictor.stock_codes)}")
            print(f"📋 前20只股票: {predictor.stock_codes[:20]}")
            
            # 检查数据文件
            import glob
            csv_files = glob.glob(os.path.join(predictor.data_dir, "*.csv"))
            print(f"📄 CSV文件数: {len(csv_files)}")
            
            # 显示一些统计信息
            print(f"\n📊 股票代码分布:")
            code_prefixes = {}
            for code in predictor.stock_codes:
                prefix = code[:3]
                code_prefixes[prefix] = code_prefixes.get(prefix, 0) + 1
            
            for prefix, count in sorted(code_prefixes.items()):
                print(f"   {prefix}xxx: {count} 只")
            
        else:
            print(f"❌ 未知命令: {command}")
            print("可用命令: all, scan, test, info")
            return
        
        print(f"\n🎉 命令执行完成！")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断操作")
    except ValueError as e:
        print(f"❌ 参数错误: {e}")
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
