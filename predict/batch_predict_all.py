#!/usr/bin/env python3
"""
批量股票预测模块

实现指定日期批量执行所有股票（data文件夹内的所有股票）预测的功能
"""

import sys
import os
import glob
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from price_range_calculator import calculate_price_range
from predict_main import predict_price_range


class BatchPredictor:
    """批量股票预测器"""
    
    def __init__(self, data_dir: str = "../data"):
        """
        初始化批量预测器
        
        参数:
        data_dir: 数据文件夹路径
        """
        self.data_dir = data_dir
        self.stock_codes = self._get_all_stock_codes()
        self.results = {}
        self.lock = threading.Lock()
        
    def _get_all_stock_codes(self) -> List[str]:
        """
        获取data文件夹中所有股票代码
        
        返回:
        List[str]: 股票代码列表
        """
        csv_files = glob.glob(os.path.join(self.data_dir, "*.csv"))
        stock_codes = []
        
        for file_path in csv_files:
            filename = os.path.basename(file_path)
            stock_code = filename.replace('.csv', '')
            # 验证股票代码格式（6位数字）
            if stock_code.isdigit() and len(stock_code) == 6:
                stock_codes.append(stock_code)
        
        return sorted(stock_codes)
    
    def predict_single_stock(self, stock_code: str, target_date: str) -> Dict:
        """
        预测单只股票
        
        参数:
        stock_code: 股票代码
        target_date: 目标日期
        
        返回:
        Dict: 预测结果
        """
        try:
            result = calculate_price_range(stock_code, target_date)
            
            # 简化结果，只保留关键信息
            if 'error' not in result:
                valid_ranges = result.get('valid_price_ranges', [])
                
                simplified_result = {
                    'success': len(valid_ranges) > 0,
                    'stock_code': stock_code,
                    'target_date': target_date,
                    'prev_close': result.get('prev_close', 0),
                    'valid_count': len(valid_ranges),
                    'conditions_met': result.get('conditions_summary', {}),
                    'board_type': '科创板' if result.get('price_limits', {}).get('is_kechuangban', False) else '主板'
                }
                
                if valid_ranges:
                    prices = [p['price'] for p in valid_ranges]
                    simplified_result.update({
                        'price_range': {
                            'min': min(prices),
                            'max': max(prices),
                            'count': len(prices)
                        },
                        'potential_return': {
                            'min_pct': (min(prices) - result['prev_close']) / result['prev_close'] * 100,
                            'max_pct': (max(prices) - result['prev_close']) / result['prev_close'] * 100
                        }
                    })
                
                return simplified_result
            else:
                return {
                    'success': False,
                    'stock_code': stock_code,
                    'target_date': target_date,
                    'error': result['error']
                }
                
        except Exception as e:
            return {
                'success': False,
                'stock_code': stock_code,
                'target_date': target_date,
                'error': str(e)
            }
    
    def batch_predict_sequential(self, target_date: str, 
                                progress_callback=None) -> Dict:
        """
        顺序批量预测所有股票
        
        参数:
        target_date: 目标日期
        progress_callback: 进度回调函数
        
        返回:
        Dict: 批量预测结果
        """
        print(f"🚀 开始顺序批量预测 {len(self.stock_codes)} 只股票...")
        print(f"📅 目标日期: {target_date}")
        
        results = {}
        successful_predictions = 0
        start_time = time.time()
        
        for i, stock_code in enumerate(self.stock_codes, 1):
            if progress_callback:
                progress_callback(i, len(self.stock_codes), stock_code)
            else:
                if i % 50 == 0 or i == len(self.stock_codes):
                    print(f"📊 进度: {i}/{len(self.stock_codes)} ({i/len(self.stock_codes)*100:.1f}%)")
            
            result = self.predict_single_stock(stock_code, target_date)
            results[stock_code] = result
            
            if result['success']:
                successful_predictions += 1
        
        end_time = time.time()
        duration = end_time - start_time
        
        summary = {
            'total_stocks': len(self.stock_codes),
            'successful_predictions': successful_predictions,
            'success_rate': f"{successful_predictions/len(self.stock_codes)*100:.1f}%",
            'duration_seconds': duration,
            'avg_time_per_stock': duration / len(self.stock_codes),
            'target_date': target_date,
            'results': results
        }
        
        return summary
    
    def batch_predict_parallel(self, target_date: str, 
                              max_workers: int = 4,
                              progress_callback=None) -> Dict:
        """
        并行批量预测所有股票
        
        参数:
        target_date: 目标日期
        max_workers: 最大并行工作线程数
        progress_callback: 进度回调函数
        
        返回:
        Dict: 批量预测结果
        """
        print(f"🚀 开始并行批量预测 {len(self.stock_codes)} 只股票...")
        print(f"📅 目标日期: {target_date}")
        print(f"🔧 并行线程数: {max_workers}")
        
        results = {}
        successful_predictions = 0
        completed_count = 0
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_stock = {
                executor.submit(self.predict_single_stock, stock_code, target_date): stock_code
                for stock_code in self.stock_codes
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_stock):
                stock_code = future_to_stock[future]
                completed_count += 1
                
                try:
                    result = future.result()
                    results[stock_code] = result
                    
                    if result['success']:
                        successful_predictions += 1
                    
                    # 进度报告
                    if progress_callback:
                        progress_callback(completed_count, len(self.stock_codes), stock_code)
                    else:
                        if completed_count % 50 == 0 or completed_count == len(self.stock_codes):
                            print(f"📊 进度: {completed_count}/{len(self.stock_codes)} "
                                  f"({completed_count/len(self.stock_codes)*100:.1f}%)")
                
                except Exception as e:
                    results[stock_code] = {
                        'success': False,
                        'stock_code': stock_code,
                        'target_date': target_date,
                        'error': f"处理异常: {str(e)}"
                    }
        
        end_time = time.time()
        duration = end_time - start_time
        
        summary = {
            'total_stocks': len(self.stock_codes),
            'successful_predictions': successful_predictions,
            'success_rate': f"{successful_predictions/len(self.stock_codes)*100:.1f}%",
            'duration_seconds': duration,
            'avg_time_per_stock': duration / len(self.stock_codes),
            'target_date': target_date,
            'parallel_workers': max_workers,
            'results': results
        }
        
        return summary
    
    def get_top_opportunities(self, batch_result: Dict, 
                             top_n: int = 10,
                             sort_by: str = 'max_return') -> List[Dict]:
        """
        获取最佳投资机会
        
        参数:
        batch_result: 批量预测结果
        top_n: 返回前N个机会
        sort_by: 排序方式 ('max_return', 'min_return', 'price_count')
        
        返回:
        List[Dict]: 排序后的投资机会列表
        """
        opportunities = []
        
        for stock_code, result in batch_result['results'].items():
            if result['success'] and 'price_range' in result:
                opportunity = {
                    'stock_code': stock_code,
                    'board_type': result['board_type'],
                    'prev_close': result['prev_close'],
                    'price_range': result['price_range'],
                    'potential_return': result['potential_return'],
                    'valid_count': result['valid_count']
                }
                opportunities.append(opportunity)
        
        # 排序
        if sort_by == 'max_return':
            opportunities.sort(key=lambda x: x['potential_return']['max_pct'], reverse=True)
        elif sort_by == 'min_return':
            opportunities.sort(key=lambda x: x['potential_return']['min_pct'], reverse=True)
        elif sort_by == 'price_count':
            opportunities.sort(key=lambda x: x['valid_count'], reverse=True)
        
        return opportunities[:top_n]
    
    def save_results_to_csv(self, batch_result: Dict, 
                           filename: Optional[str] = None) -> str:
        """
        保存批量预测结果到CSV文件
        
        参数:
        batch_result: 批量预测结果
        filename: 输出文件名，如果为None则自动生成
        
        返回:
        str: 保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            target_date = batch_result['target_date'].replace('-', '')
            filename = f"batch_prediction_{target_date}_{timestamp}.csv"
        
        # 准备数据
        data_rows = []
        for stock_code, result in batch_result['results'].items():
            row = {
                'stock_code': stock_code,
                'success': result['success'],
                'target_date': result['target_date'],
                'board_type': result.get('board_type', ''),
                'prev_close': result.get('prev_close', 0),
                'valid_count': result.get('valid_count', 0),
                'error': result.get('error', '')
            }
            
            if result['success'] and 'price_range' in result:
                row.update({
                    'price_min': result['price_range']['min'],
                    'price_max': result['price_range']['max'],
                    'price_count': result['price_range']['count'],
                    'min_return_pct': result['potential_return']['min_pct'],
                    'max_return_pct': result['potential_return']['max_pct']
                })
            else:
                row.update({
                    'price_min': 0,
                    'price_max': 0,
                    'price_count': 0,
                    'min_return_pct': 0,
                    'max_return_pct': 0
                })
            
            data_rows.append(row)
        
        # 创建DataFrame并保存
        df = pd.DataFrame(data_rows)
        df = df.sort_values(['success', 'max_return_pct'], ascending=[False, False])
        
        filepath = os.path.join(os.path.dirname(__file__), filename)
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        return filepath


def print_batch_summary(batch_result: Dict):
    """
    打印批量预测结果摘要
    
    参数:
    batch_result: 批量预测结果
    """
    print("\n" + "="*80)
    print("📊 批量股票预测结果摘要")
    print("="*80)
    
    print(f"📅 目标日期: {batch_result['target_date']}")
    print(f"📈 总股票数: {batch_result['total_stocks']}")
    print(f"✅ 成功预测: {batch_result['successful_predictions']}")
    print(f"🎯 成功率: {batch_result['success_rate']}")
    print(f"⏱️  总耗时: {batch_result['duration_seconds']:.1f} 秒")
    print(f"📊 平均耗时: {batch_result['avg_time_per_stock']:.2f} 秒/股")
    
    if 'parallel_workers' in batch_result:
        print(f"🔧 并行线程: {batch_result['parallel_workers']}")
    
    # 统计各板块情况
    main_board_success = 0
    kechuang_board_success = 0
    main_board_total = 0
    kechuang_board_total = 0
    
    for result in batch_result['results'].values():
        if result.get('board_type') == '科创板':
            kechuang_board_total += 1
            if result['success']:
                kechuang_board_success += 1
        else:
            main_board_total += 1
            if result['success']:
                main_board_success += 1
    
    print(f"\n📋 分板块统计:")
    if main_board_total > 0:
        print(f"   主板: {main_board_success}/{main_board_total} "
              f"({main_board_success/main_board_total*100:.1f}%)")
    if kechuang_board_total > 0:
        print(f"   科创板: {kechuang_board_success}/{kechuang_board_total} "
              f"({kechuang_board_success/kechuang_board_total*100:.1f}%)")


# 简化的批量预测接口函数
def batch_predict_all_stocks(target_date: str,
                            parallel: bool = True,
                            max_workers: int = 4,
                            save_csv: bool = True,
                            show_top: int = 10) -> Dict:
    """
    批量预测所有股票的简化接口

    参数:
    target_date: 目标日期，格式 'YYYY-MM-DD'
    parallel: 是否使用并行处理
    max_workers: 并行线程数
    save_csv: 是否保存结果到CSV
    show_top: 显示前N个最佳机会

    返回:
    Dict: 批量预测结果
    """
    predictor = BatchPredictor()

    print(f"🚀 批量预测所有股票 (共 {len(predictor.stock_codes)} 只)")
    print(f"📅 目标日期: {target_date}")

    # 执行预测
    if parallel:
        batch_result = predictor.batch_predict_parallel(target_date, max_workers)
    else:
        batch_result = predictor.batch_predict_sequential(target_date)

    # 打印摘要
    print_batch_summary(batch_result)

    # 显示最佳机会
    if batch_result['successful_predictions'] > 0 and show_top > 0:
        print(f"\n✨ 前 {show_top} 个最佳投资机会:")
        top_opportunities = predictor.get_top_opportunities(
            batch_result, top_n=show_top, sort_by='max_return'
        )

        for i, opp in enumerate(top_opportunities, 1):
            print(f"   {i:2d}. {opp['stock_code']} ({opp['board_type']}): "
                  f"价格 {opp['price_range']['min']:.2f}-{opp['price_range']['max']:.2f}, "
                  f"收益 {opp['potential_return']['min_pct']:.1f}%-{opp['potential_return']['max_pct']:.1f}%")

    # 保存CSV
    if save_csv and batch_result['successful_predictions'] > 0:
        csv_file = predictor.save_results_to_csv(batch_result)
        print(f"\n💾 详细结果已保存到: {csv_file}")

    return batch_result


def quick_scan_opportunities(target_date: str, min_return: float = 5.0) -> List[Dict]:
    """
    快速扫描投资机会

    参数:
    target_date: 目标日期
    min_return: 最小收益率要求 (%)

    返回:
    List[Dict]: 符合条件的投资机会
    """
    print(f"🔍 快速扫描投资机会 (最小收益率: {min_return}%)")

    batch_result = batch_predict_all_stocks(
        target_date,
        parallel=True,
        save_csv=False,
        show_top=0
    )

    # 筛选符合收益率要求的机会
    opportunities = []
    for stock_code, result in batch_result['results'].items():
        if (result['success'] and
            'potential_return' in result and
            result['potential_return']['max_pct'] >= min_return):

            opportunities.append({
                'stock_code': stock_code,
                'max_return_pct': result['potential_return']['max_pct'],
                'price_range': result['price_range'],
                'board_type': result['board_type']
            })

    # 按收益率排序
    opportunities.sort(key=lambda x: x['max_return_pct'], reverse=True)

    print(f"\n✅ 发现 {len(opportunities)} 个符合条件的投资机会:")
    for i, opp in enumerate(opportunities[:10], 1):
        print(f"   {i}. {opp['stock_code']}: {opp['max_return_pct']:.1f}% "
              f"({opp['price_range']['min']:.2f}-{opp['price_range']['max']:.2f})")

    return opportunities


def main():
    """主函数 - 批量预测示例"""
    print("🚀 批量股票预测功能演示")
    print("="*60)

    # 示例1: 基本批量预测
    target_date = "2025-03-10"
    batch_result = batch_predict_all_stocks(target_date)

    print("\n" + "="*60)

    # 示例2: 快速机会扫描
    print("🔍 示例2: 快速机会扫描")
    opportunities = quick_scan_opportunities(target_date, min_return=3.0)

    print(f"\n🎉 演示完成！")
    print(f"💡 使用提示:")
    print(f"   - 使用 batch_predict_all_stocks() 进行完整批量预测")
    print(f"   - 使用 quick_scan_opportunities() 快速筛选高收益机会")


if __name__ == "__main__":
    main()
