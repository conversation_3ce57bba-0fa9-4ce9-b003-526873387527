#!/usr/bin/env python3
"""
批量预测功能测试脚本

测试批量股票预测功能的正确性和性能
"""

import sys
import os
from datetime import datetime
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from batch_predict_all import BatchPredictor, batch_predict_all_stocks, quick_scan_opportunities


def test_batch_predictor_init():
    """测试批量预测器初始化"""
    print("🧪 测试批量预测器初始化")
    print("="*50)
    
    try:
        predictor = BatchPredictor()
        
        print(f"✅ 初始化成功")
        print(f"   数据目录: {predictor.data_dir}")
        print(f"   发现股票数: {len(predictor.stock_codes)}")
        print(f"   前10只股票: {predictor.stock_codes[:10]}")
        
        # 验证股票代码格式
        valid_codes = 0
        for code in predictor.stock_codes[:20]:  # 检查前20个
            if code.isdigit() and len(code) == 6:
                valid_codes += 1
        
        print(f"   股票代码格式验证: {valid_codes}/20 正确")
        
        return len(predictor.stock_codes) > 0
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False


def test_single_stock_prediction():
    """测试单只股票预测"""
    print("\n🧪 测试单只股票预测")
    print("="*50)
    
    try:
        predictor = BatchPredictor()
        
        # 选择一只测试股票
        test_stock = "600066"
        target_date = "2025-03-10"
        
        print(f"📊 测试股票: {test_stock}")
        print(f"📅 目标日期: {target_date}")
        
        result = predictor.predict_single_stock(test_stock, target_date)
        
        print(f"✅ 预测完成")
        print(f"   成功: {result['success']}")
        print(f"   股票代码: {result['stock_code']}")
        print(f"   目标日期: {result['target_date']}")
        
        if result['success']:
            print(f"   有效价格点: {result['valid_count']}")
            if 'price_range' in result:
                print(f"   价格范围: {result['price_range']['min']:.2f} - {result['price_range']['max']:.2f}")
        else:
            print(f"   错误信息: {result.get('error', '未知错误')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 单只股票预测测试失败: {e}")
        return False


def test_small_batch_sequential():
    """测试小批量顺序预测"""
    print("\n🧪 测试小批量顺序预测")
    print("="*50)
    
    try:
        predictor = BatchPredictor()
        
        # 只测试前5只股票
        original_codes = predictor.stock_codes
        predictor.stock_codes = original_codes[:5]
        
        target_date = "2025-03-10"
        
        print(f"📊 测试股票数: {len(predictor.stock_codes)}")
        print(f"📅 目标日期: {target_date}")
        
        start_time = time.time()
        result = predictor.batch_predict_sequential(target_date)
        end_time = time.time()
        
        print(f"✅ 顺序预测完成")
        print(f"   总股票数: {result['total_stocks']}")
        print(f"   成功预测: {result['successful_predictions']}")
        print(f"   成功率: {result['success_rate']}")
        print(f"   耗时: {end_time - start_time:.2f} 秒")
        
        # 恢复原始股票列表
        predictor.stock_codes = original_codes
        
        return result['total_stocks'] == 5
        
    except Exception as e:
        print(f"❌ 小批量顺序预测测试失败: {e}")
        return False


def test_small_batch_parallel():
    """测试小批量并行预测"""
    print("\n🧪 测试小批量并行预测")
    print("="*50)
    
    try:
        predictor = BatchPredictor()
        
        # 只测试前5只股票
        original_codes = predictor.stock_codes
        predictor.stock_codes = original_codes[:5]
        
        target_date = "2025-03-10"
        
        print(f"📊 测试股票数: {len(predictor.stock_codes)}")
        print(f"📅 目标日期: {target_date}")
        
        start_time = time.time()
        result = predictor.batch_predict_parallel(target_date, max_workers=2)
        end_time = time.time()
        
        print(f"✅ 并行预测完成")
        print(f"   总股票数: {result['total_stocks']}")
        print(f"   成功预测: {result['successful_predictions']}")
        print(f"   成功率: {result['success_rate']}")
        print(f"   耗时: {end_time - start_time:.2f} 秒")
        print(f"   并行线程: {result['parallel_workers']}")
        
        # 恢复原始股票列表
        predictor.stock_codes = original_codes
        
        return result['total_stocks'] == 5
        
    except Exception as e:
        print(f"❌ 小批量并行预测测试失败: {e}")
        return False


def test_top_opportunities():
    """测试最佳机会筛选"""
    print("\n🧪 测试最佳机会筛选")
    print("="*50)
    
    try:
        predictor = BatchPredictor()
        
        # 创建模拟的批量预测结果
        mock_result = {
            'total_stocks': 3,
            'successful_predictions': 2,
            'results': {
                '600066': {
                    'success': True,
                    'stock_code': '600066',
                    'board_type': '主板',
                    'prev_close': 25.0,
                    'valid_count': 10,
                    'price_range': {'min': 25.5, 'max': 26.0, 'count': 10},
                    'potential_return': {'min_pct': 2.0, 'max_pct': 4.0}
                },
                '000001': {
                    'success': True,
                    'stock_code': '000001',
                    'board_type': '主板',
                    'prev_close': 15.0,
                    'valid_count': 5,
                    'price_range': {'min': 15.3, 'max': 15.8, 'count': 5},
                    'potential_return': {'min_pct': 2.0, 'max_pct': 5.3}
                },
                '000002': {
                    'success': False,
                    'stock_code': '000002',
                    'error': '不满足条件'
                }
            }
        }
        
        top_opportunities = predictor.get_top_opportunities(
            mock_result, 
            top_n=2, 
            sort_by='max_return'
        )
        
        print(f"✅ 机会筛选完成")
        print(f"   发现机会数: {len(top_opportunities)}")
        
        for i, opp in enumerate(top_opportunities, 1):
            print(f"   {i}. {opp['stock_code']}: 最大收益 {opp['potential_return']['max_pct']:.1f}%")
        
        # 验证排序是否正确（按最大收益降序）
        if len(top_opportunities) >= 2:
            return top_opportunities[0]['potential_return']['max_pct'] >= top_opportunities[1]['potential_return']['max_pct']
        
        return len(top_opportunities) > 0
        
    except Exception as e:
        print(f"❌ 最佳机会筛选测试失败: {e}")
        return False


def test_csv_export():
    """测试CSV导出功能"""
    print("\n🧪 测试CSV导出功能")
    print("="*50)
    
    try:
        predictor = BatchPredictor()
        
        # 创建模拟的批量预测结果
        mock_result = {
            'target_date': '2025-03-10',
            'total_stocks': 2,
            'successful_predictions': 1,
            'results': {
                '600066': {
                    'success': True,
                    'stock_code': '600066',
                    'target_date': '2025-03-10',
                    'board_type': '主板',
                    'prev_close': 25.0,
                    'valid_count': 10,
                    'price_range': {'min': 25.5, 'max': 26.0, 'count': 10},
                    'potential_return': {'min_pct': 2.0, 'max_pct': 4.0}
                },
                '000001': {
                    'success': False,
                    'stock_code': '000001',
                    'target_date': '2025-03-10',
                    'error': '不满足条件'
                }
            }
        }
        
        # 导出CSV
        csv_file = predictor.save_results_to_csv(mock_result, 'test_batch_result.csv')
        
        print(f"✅ CSV导出完成")
        print(f"   文件路径: {csv_file}")
        
        # 检查文件是否存在
        if os.path.exists(csv_file):
            file_size = os.path.getsize(csv_file)
            print(f"   文件大小: {file_size} 字节")
            
            # 清理测试文件
            os.remove(csv_file)
            print(f"   测试文件已清理")
            
            return True
        else:
            print(f"❌ CSV文件未生成")
            return False
        
    except Exception as e:
        print(f"❌ CSV导出测试失败: {e}")
        return False


def test_simplified_interface():
    """测试简化接口函数"""
    print("\n🧪 测试简化接口函数")
    print("="*50)
    
    try:
        # 临时修改BatchPredictor以减少测试时间
        original_get_all_stock_codes = BatchPredictor._get_all_stock_codes
        
        def mock_get_all_stock_codes(self):
            return ['600066', '000001', '000002']  # 只返回3只股票用于测试
        
        BatchPredictor._get_all_stock_codes = mock_get_all_stock_codes
        
        target_date = "2025-03-10"
        
        print(f"📅 目标日期: {target_date}")
        print(f"🔧 使用简化接口进行测试...")
        
        # 测试简化接口
        result = batch_predict_all_stocks(
            target_date, 
            parallel=True, 
            max_workers=2, 
            save_csv=False, 
            show_top=3
        )
        
        print(f"✅ 简化接口测试完成")
        print(f"   总股票数: {result['total_stocks']}")
        print(f"   成功预测: {result['successful_predictions']}")
        
        # 恢复原始方法
        BatchPredictor._get_all_stock_codes = original_get_all_stock_codes
        
        return result['total_stocks'] == 3
        
    except Exception as e:
        print(f"❌ 简化接口测试失败: {e}")
        # 确保恢复原始方法
        BatchPredictor._get_all_stock_codes = original_get_all_stock_codes
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行批量预测功能测试套件")
    print("="*60)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("批量预测器初始化测试", test_batch_predictor_init),
        ("单只股票预测测试", test_single_stock_prediction),
        ("小批量顺序预测测试", test_small_batch_sequential),
        ("小批量并行预测测试", test_small_batch_parallel),
        ("最佳机会筛选测试", test_top_opportunities),
        ("CSV导出功能测试", test_csv_export),
        ("简化接口函数测试", test_simplified_interface)
    ]
    
    for test_name, test_func in tests:
        print(f"\n🔄 执行 {test_name}...")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed_count = sum(1 for _, result in test_results if result)
    total_count = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📈 总体结果: {passed_count}/{total_count} 测试通过")
    print(f"🎯 成功率: {passed_count/total_count*100:.1f}%")
    
    if passed_count == total_count:
        print("\n🎉 所有测试通过！批量预测功能正常工作。")
    else:
        print(f"\n⚠️  有 {total_count - passed_count} 个测试失败，请检查相关功能。")
    
    return passed_count == total_count


if __name__ == "__main__":
    # 运行测试套件
    success = run_all_tests()
    
    if success:
        print("\n🎊 测试完成，批量预测功能可以正常使用！")
        print("\n💡 使用示例:")
        print("   from batch_predict_all import batch_predict_all_stocks")
        print("   result = batch_predict_all_stocks('2025-03-10')")
    else:
        print("\n⚠️  测试发现问题，请修复后再使用。")
    
    sys.exit(0 if success else 1)
