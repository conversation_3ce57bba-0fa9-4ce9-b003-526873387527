#!/usr/bin/env python3
"""
批量股票预测功能使用示例

演示如何使用批量预测模块来分析所有股票的投资机会
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from batch_predict_all import (
    BatchPredictor, 
    batch_predict_all_stocks, 
    quick_scan_opportunities,
    print_batch_summary
)


def example_1_basic_batch_prediction():
    """示例1: 基本批量预测"""
    print("📊 示例1: 基本批量预测")
    print("="*60)
    
    target_date = "2025-03-10"
    
    print(f"🎯 对所有股票进行批量预测")
    print(f"📅 目标日期: {target_date}")
    print(f"⚠️  注意: 这将分析所有股票，可能需要较长时间...")
    
    # 使用简化接口进行批量预测
    result = batch_predict_all_stocks(
        target_date=target_date,
        parallel=True,          # 使用并行处理
        max_workers=4,          # 4个并行线程
        save_csv=True,          # 保存结果到CSV
        show_top=10             # 显示前10个最佳机会
    )
    
    print(f"\n📊 预测完成!")
    print(f"   总股票数: {result['total_stocks']}")
    print(f"   发现机会: {result['successful_predictions']}")
    print(f"   成功率: {result['success_rate']}")
    
    return result


def example_2_quick_opportunity_scan():
    """示例2: 快速机会扫描"""
    print("\n📊 示例2: 快速机会扫描")
    print("="*60)
    
    target_date = "2025-03-10"
    min_return = 5.0  # 最小收益率要求5%
    
    print(f"🔍 快速扫描高收益投资机会")
    print(f"📅 目标日期: {target_date}")
    print(f"💰 最小收益率要求: {min_return}%")
    
    # 快速扫描机会
    opportunities = quick_scan_opportunities(target_date, min_return)
    
    if opportunities:
        print(f"\n✨ 发现 {len(opportunities)} 个高收益机会:")
        for i, opp in enumerate(opportunities[:5], 1):
            print(f"   {i}. {opp['stock_code']} ({opp['board_type']}): "
                  f"最大收益 {opp['max_return_pct']:.1f}%")
    else:
        print(f"\n💤 未发现满足 {min_return}% 收益率要求的机会")
    
    return opportunities


def example_3_custom_batch_prediction():
    """示例3: 自定义批量预测"""
    print("\n📊 示例3: 自定义批量预测")
    print("="*60)
    
    # 创建自定义批量预测器
    predictor = BatchPredictor()
    
    print(f"📊 发现 {len(predictor.stock_codes)} 只股票")
    
    # 只分析前50只股票作为演示
    predictor.stock_codes = predictor.stock_codes[:50]
    target_date = "2025-03-10"
    
    print(f"🎯 自定义分析前 {len(predictor.stock_codes)} 只股票")
    print(f"📅 目标日期: {target_date}")
    
    # 执行并行预测
    result = predictor.batch_predict_parallel(
        target_date, 
        max_workers=2  # 使用2个线程
    )
    
    # 打印结果摘要
    print_batch_summary(result)
    
    # 获取最佳机会
    if result['successful_predictions'] > 0:
        print(f"\n✨ 最佳投资机会:")
        top_opportunities = predictor.get_top_opportunities(
            result, 
            top_n=5, 
            sort_by='max_return'
        )
        
        for i, opp in enumerate(top_opportunities, 1):
            print(f"   {i}. {opp['stock_code']}: "
                  f"收益 {opp['potential_return']['max_pct']:.1f}%, "
                  f"价格 {opp['price_range']['min']:.2f}-{opp['price_range']['max']:.2f}")
        
        # 保存结果
        csv_file = predictor.save_results_to_csv(result, "custom_batch_result.csv")
        print(f"\n💾 结果已保存到: {csv_file}")
    
    return result


def example_4_multi_date_analysis():
    """示例4: 多日期分析"""
    print("\n📊 示例4: 多日期分析")
    print("="*60)
    
    # 分析多个日期的投资机会
    base_date = datetime(2025, 3, 1)
    test_dates = []
    
    # 生成5个测试日期
    for i in range(0, 10, 2):  # 每隔2天一个日期
        test_date = base_date + timedelta(days=i)
        test_dates.append(test_date.strftime('%Y-%m-%d'))
    
    print(f"🗓️  分析日期: {test_dates}")
    
    # 创建批量预测器，只分析前20只股票
    predictor = BatchPredictor()
    predictor.stock_codes = predictor.stock_codes[:20]
    
    all_opportunities = {}
    
    for date in test_dates:
        print(f"\n📅 分析日期: {date}")
        
        result = predictor.batch_predict_parallel(date, max_workers=2)
        
        print(f"   成功预测: {result['successful_predictions']}/{result['total_stocks']}")
        
        if result['successful_predictions'] > 0:
            opportunities = predictor.get_top_opportunities(result, top_n=3)
            all_opportunities[date] = opportunities
            
            print(f"   最佳机会:")
            for i, opp in enumerate(opportunities, 1):
                print(f"     {i}. {opp['stock_code']}: {opp['potential_return']['max_pct']:.1f}%")
    
    # 汇总分析
    print(f"\n📊 多日期分析汇总:")
    total_opportunity_days = len(all_opportunities)
    print(f"   有机会的日期数: {total_opportunity_days}/{len(test_dates)}")
    
    if total_opportunity_days > 0:
        # 统计最常出现的股票
        stock_frequency = {}
        for date, opportunities in all_opportunities.items():
            for opp in opportunities:
                stock_code = opp['stock_code']
                stock_frequency[stock_code] = stock_frequency.get(stock_code, 0) + 1
        
        print(f"   最常出现的机会股票:")
        sorted_stocks = sorted(stock_frequency.items(), key=lambda x: x[1], reverse=True)
        for stock_code, freq in sorted_stocks[:5]:
            print(f"     {stock_code}: 出现 {freq} 次")
    
    return all_opportunities


def example_5_performance_comparison():
    """示例5: 性能对比"""
    print("\n📊 示例5: 顺序vs并行性能对比")
    print("="*60)
    
    # 创建批量预测器，只分析前10只股票
    predictor = BatchPredictor()
    predictor.stock_codes = predictor.stock_codes[:10]
    target_date = "2025-03-10"
    
    print(f"🎯 性能测试: 分析 {len(predictor.stock_codes)} 只股票")
    
    # 测试顺序处理
    print(f"\n🔄 测试顺序处理...")
    import time
    start_time = time.time()
    sequential_result = predictor.batch_predict_sequential(target_date)
    sequential_time = time.time() - start_time
    
    # 测试并行处理
    print(f"\n🔄 测试并行处理...")
    start_time = time.time()
    parallel_result = predictor.batch_predict_parallel(target_date, max_workers=2)
    parallel_time = time.time() - start_time
    
    # 性能对比
    print(f"\n⚡ 性能对比结果:")
    print(f"   顺序处理: {sequential_time:.2f} 秒")
    print(f"   并行处理: {parallel_time:.2f} 秒")
    
    if parallel_time < sequential_time:
        speedup = sequential_time / parallel_time
        print(f"   并行加速比: {speedup:.2f}x")
    else:
        print(f"   并行处理未显示明显优势（可能因为股票数量较少）")
    
    return {
        'sequential_time': sequential_time,
        'parallel_time': parallel_time,
        'sequential_result': sequential_result,
        'parallel_result': parallel_result
    }


def main():
    """主函数 - 运行所有示例"""
    print("🚀 批量股票预测功能使用示例")
    print("="*80)
    print("本示例将演示如何使用批量预测模块进行大规模股票分析")
    print("="*80)
    
    try:
        # 示例1: 基本批量预测（注释掉以避免长时间运行）
        # print("⚠️  示例1需要较长时间，已跳过。如需运行请取消注释。")
        # example_1_basic_batch_prediction()
        
        # 示例2: 快速机会扫描（注释掉以避免长时间运行）
        # print("⚠️  示例2需要较长时间，已跳过。如需运行请取消注释。")
        # example_2_quick_opportunity_scan()
        
        # 示例3: 自定义批量预测
        example_3_custom_batch_prediction()
        
        # 示例4: 多日期分析
        example_4_multi_date_analysis()
        
        # 示例5: 性能对比
        example_5_performance_comparison()
        
        print("\n\n🎉 所有示例运行完成！")
        print("\n💡 使用提示:")
        print("   1. 对于全量股票分析，建议使用并行处理以提高效率")
        print("   2. 可以根据需要调整最小收益率要求来筛选机会")
        print("   3. 批量预测结果会自动保存为CSV文件便于后续分析")
        print("   4. 技术指标条件较严格，实际投资机会可能较少")
        print("   5. 建议结合多个日期的分析结果制定投资策略")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")


if __name__ == "__main__":
    main()
