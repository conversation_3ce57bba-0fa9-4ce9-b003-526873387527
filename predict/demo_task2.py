#!/usr/bin/env python3
"""
任务2演示脚本

演示批量股票预测功能的核心特性
"""

import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from batch_predict_all import BatchPredictor, batch_predict_all_stocks, quick_scan_opportunities


def demo_task2_basic():
    """演示任务2的基本功能"""
    print("🚀 任务2演示：批量股票预测功能")
    print("="*60)
    
    # 1. 显示可分析的股票数量
    predictor = BatchPredictor()
    total_stocks = len(predictor.stock_codes)
    
    print(f"📊 数据统计:")
    print(f"   发现股票数量: {total_stocks}")
    print(f"   数据文件目录: {predictor.data_dir}")
    print(f"   前10只股票: {predictor.stock_codes[:10]}")
    
    # 2. 小规模批量预测演示
    print(f"\n🎯 小规模批量预测演示 (前20只股票)")
    
    # 只分析前20只股票作为演示
    predictor.stock_codes = predictor.stock_codes[:20]
    target_date = "2025-03-10"
    
    print(f"📅 目标日期: {target_date}")
    print(f"📈 分析股票: {predictor.stock_codes}")
    
    # 执行并行批量预测
    result = predictor.batch_predict_parallel(target_date, max_workers=2)
    
    # 显示结果
    print(f"\n📊 预测结果:")
    print(f"   总股票数: {result['total_stocks']}")
    print(f"   成功预测: {result['successful_predictions']}")
    print(f"   成功率: {result['success_rate']}")
    print(f"   耗时: {result['duration_seconds']:.2f} 秒")
    print(f"   平均耗时: {result['avg_time_per_stock']:.3f} 秒/股")
    
    # 3. 显示预测详情
    print(f"\n📋 详细预测结果:")
    success_count = 0
    fail_count = 0
    
    for stock_code, stock_result in result['results'].items():
        if stock_result['success']:
            success_count += 1
            price_range = stock_result['price_range']
            potential_return = stock_result['potential_return']
            print(f"   ✅ {stock_code}: 价格 {price_range['min']:.2f}-{price_range['max']:.2f}, "
                  f"收益 {potential_return['min_pct']:.1f}%-{potential_return['max_pct']:.1f}%")
        else:
            fail_count += 1
            error_msg = stock_result.get('error', '未知错误')
            # 只显示前几个失败的例子
            if fail_count <= 3:
                print(f"   ❌ {stock_code}: {error_msg}")
    
    if fail_count > 3:
        print(f"   ... 还有 {fail_count - 3} 只股票未满足条件")
    
    # 4. 获取最佳投资机会
    if result['successful_predictions'] > 0:
        print(f"\n✨ 最佳投资机会:")
        top_opportunities = predictor.get_top_opportunities(
            result, 
            top_n=min(5, result['successful_predictions']), 
            sort_by='max_return'
        )
        
        for i, opp in enumerate(top_opportunities, 1):
            print(f"   {i}. {opp['stock_code']} ({opp['board_type']}): "
                  f"最大收益 {opp['potential_return']['max_pct']:.1f}%, "
                  f"价格范围 {opp['price_range']['min']:.2f}-{opp['price_range']['max']:.2f}")
        
        # 5. 保存结果到CSV
        csv_file = predictor.save_results_to_csv(result, "demo_batch_result.csv")
        print(f"\n💾 详细结果已保存到: {csv_file}")
        
    else:
        print(f"\n💤 当前日期没有发现满足条件的投资机会")
        print(f"   这是正常现象，技术指标条件较为严格")
    
    return result


def demo_performance_comparison():
    """演示顺序vs并行处理的性能对比"""
    print(f"\n🔧 性能对比演示：顺序 vs 并行处理")
    print("="*60)
    
    predictor = BatchPredictor()
    # 使用前15只股票进行性能测试
    predictor.stock_codes = predictor.stock_codes[:15]
    target_date = "2025-03-10"
    
    print(f"📊 测试配置:")
    print(f"   股票数量: {len(predictor.stock_codes)}")
    print(f"   目标日期: {target_date}")
    
    import time
    
    # 测试顺序处理
    print(f"\n🔄 测试顺序处理...")
    start_time = time.time()
    sequential_result = predictor.batch_predict_sequential(target_date)
    sequential_time = time.time() - start_time
    
    # 测试并行处理
    print(f"\n🔄 测试并行处理 (2线程)...")
    start_time = time.time()
    parallel_result = predictor.batch_predict_parallel(target_date, max_workers=2)
    parallel_time = time.time() - start_time
    
    # 性能对比
    print(f"\n⚡ 性能对比结果:")
    print(f"   顺序处理: {sequential_time:.2f} 秒")
    print(f"   并行处理: {parallel_time:.2f} 秒")
    
    if parallel_time < sequential_time:
        speedup = sequential_time / parallel_time
        print(f"   并行加速比: {speedup:.2f}x")
        print(f"   ✅ 并行处理更快")
    else:
        print(f"   ⚠️  并行处理未显示明显优势（可能因为股票数量较少）")
    
    # 验证结果一致性
    seq_success = sequential_result['successful_predictions']
    par_success = parallel_result['successful_predictions']
    
    print(f"\n🔍 结果一致性验证:")
    print(f"   顺序处理成功数: {seq_success}")
    print(f"   并行处理成功数: {par_success}")
    
    if seq_success == par_success:
        print(f"   ✅ 结果一致")
    else:
        print(f"   ⚠️  结果不一致，可能存在并发问题")


def demo_simplified_interface():
    """演示简化接口的使用"""
    print(f"\n🎯 简化接口演示")
    print("="*60)
    
    # 临时修改以减少演示时间
    original_get_all_stock_codes = BatchPredictor._get_all_stock_codes
    
    def mock_get_all_stock_codes(self):
        return ['600066', '000001', '000002', '600519', '000858']  # 只返回5只股票
    
    BatchPredictor._get_all_stock_codes = mock_get_all_stock_codes
    
    try:
        target_date = "2025-03-10"
        
        print(f"📅 目标日期: {target_date}")
        print(f"🔧 使用简化接口进行批量预测...")
        
        # 使用简化接口
        result = batch_predict_all_stocks(
            target_date, 
            parallel=True, 
            max_workers=2, 
            save_csv=False,  # 不保存CSV以简化演示
            show_top=3
        )
        
        print(f"\n📊 简化接口演示完成:")
        print(f"   接口调用简单，一行代码完成批量预测")
        print(f"   自动显示最佳机会和结果摘要")
        print(f"   支持灵活的参数配置")
        
    finally:
        # 恢复原始方法
        BatchPredictor._get_all_stock_codes = original_get_all_stock_codes


def main():
    """主演示函数"""
    print("🎉 任务2：批量股票预测功能演示")
    print("="*80)
    print("本演示将展示批量预测功能的核心特性和优势")
    print("="*80)
    
    try:
        # 1. 基本功能演示
        demo_task2_basic()
        
        # 2. 性能对比演示
        demo_performance_comparison()
        
        # 3. 简化接口演示
        demo_simplified_interface()
        
        print(f"\n🎊 任务2演示完成！")
        print(f"\n💡 任务2核心特性总结:")
        print(f"   ✅ 支持批量分析data文件夹内所有股票")
        print(f"   ✅ 提供并行处理以提高分析效率")
        print(f"   ✅ 自动筛选和排序最佳投资机会")
        print(f"   ✅ 支持结果导出为CSV文件")
        print(f"   ✅ 提供简化接口便于使用")
        print(f"   ✅ 支持自定义参数和灵活配置")
        
        print(f"\n🚀 使用建议:")
        print(f"   - 对于全量股票分析，建议使用并行处理")
        print(f"   - 可根据需要调整线程数以平衡性能和资源使用")
        print(f"   - 批量预测结果可保存为CSV便于后续分析")
        print(f"   - 建议结合多个日期的分析结果制定投资策略")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
