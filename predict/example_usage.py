#!/usr/bin/env python3
"""
股票价格范围预测功能使用示例

演示如何使用价格预测模块来分析股票的投资机会
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from predict_main import predict_price_range, batch_predict, print_prediction_report


def example_1_basic_prediction():
    """示例1: 基本的单只股票预测"""
    print("📊 示例1: 基本股票价格范围预测")
    print("="*60)
    
    stock_code = "600066"
    target_date = "2025-03-10"  # 使用一个可能有结果的日期
    
    print(f"🎯 预测股票 {stock_code} 在 {target_date} 的价格范围")
    
    # 执行预测
    result = predict_price_range(stock_code, target_date, detailed=True)
    
    if result.get('success', False):
        print_prediction_report(result)
        
        # 提取关键信息
        if result['prediction_results']['has_valid_range']:
            price_range = result['prediction_results']['price_range']
            prev_close = result['stock_info']['prev_close']
            
            print(f"\n💡 投资建议:")
            print(f"   前收盘价: {prev_close:.2f}")
            print(f"   建议关注价格区间: {price_range['min']:.2f} - {price_range['max']:.2f}")
            
            # 计算潜在收益
            min_return = (price_range['min'] - prev_close) / prev_close * 100
            max_return = (price_range['max'] - prev_close) / prev_close * 100
            print(f"   潜在收益范围: {min_return:.1f}% - {max_return:.1f}%")
        else:
            print(f"\n⚠️  该日期没有满足条件的价格范围")
    else:
        print(f"❌ 预测失败: {result.get('error', '未知错误')}")


def example_2_batch_analysis():
    """示例2: 批量股票分析"""
    print("\n\n📊 示例2: 批量股票分析")
    print("="*60)
    
    # 选择一些热门股票进行分析
    stock_list = ["600066", "000001", "000002", "600519", "000858"]
    
    print(f"🔍 分析 {len(stock_list)} 只股票的投资机会...")
    
    # 执行批量预测
    batch_result = batch_predict(stock_list)
    
    print(f"\n📈 批量分析结果:")
    print(f"   分析股票数: {batch_result['total_stocks']}")
    print(f"   发现机会: {batch_result['successful_predictions']}")
    print(f"   机会发现率: {batch_result['success_rate']}")
    
    # 显示有投资机会的股票
    opportunities = []
    for stock_code, result in batch_result['results'].items():
        if result.get('success', False):
            opportunities.append((stock_code, result))
    
    if opportunities:
        print(f"\n✨ 发现 {len(opportunities)} 个投资机会:")
        for stock_code, result in opportunities:
            range_info = result['valid_price_range']
            prev_close = result['recommendation']['prev_close']
            
            # 计算中位收益
            mid_price = (range_info['min'] + range_info['max']) / 2
            potential_return = (mid_price - prev_close) / prev_close * 100
            
            print(f"   📈 {stock_code}: 价格区间 {range_info['min']:.2f}-{range_info['max']:.2f}, "
                  f"潜在收益 {potential_return:.1f}%")
    else:
        print(f"\n💤 当前没有发现满足条件的投资机会")
        print(f"   建议稍后再次分析或调整筛选条件")


def example_3_investment_strategy():
    """示例3: 投资策略制定"""
    print("\n\n📊 示例3: 基于技术指标的投资策略")
    print("="*60)
    
    stock_code = "600066"
    
    # 分析最近几个交易日的机会
    print(f"🎯 分析 {stock_code} 最近的投资机会...")
    
    # 生成最近几个交易日的日期
    base_date = datetime(2025, 3, 1)
    test_dates = []
    for i in range(20):  # 测试20个交易日
        test_date = base_date + timedelta(days=i)
        test_dates.append(test_date.strftime('%Y-%m-%d'))
    
    opportunities = []
    
    for date in test_dates:
        try:
            result = predict_price_range(stock_code, date, detailed=False)
            if result.get('success', False):
                opportunities.append((date, result))
        except:
            continue
    
    if opportunities:
        print(f"\n✅ 在测试期间发现 {len(opportunities)} 个交易机会:")
        
        for date, result in opportunities:
            range_info = result['valid_price_range']
            prev_close = result['recommendation']['prev_close']
            
            # 计算风险收益比
            mid_price = (range_info['min'] + range_info['max']) / 2
            potential_return = (mid_price - prev_close) / prev_close * 100
            price_volatility = (range_info['max'] - range_info['min']) / prev_close * 100
            
            print(f"   📅 {date}: 目标价 {mid_price:.2f} (收益 {potential_return:.1f}%, "
                  f"波动 {price_volatility:.1f}%)")
        
        # 投资策略建议
        print(f"\n💡 投资策略建议:")
        print(f"   1. 关注技术指标信号，等待满足条件的交易日")
        print(f"   2. 在预测价格范围内设置买入订单")
        print(f"   3. 设置合理的止盈止损点位")
        print(f"   4. 控制仓位，分散投资风险")
        
    else:
        print(f"\n💤 测试期间未发现满足条件的交易机会")
        print(f"   建议继续观察或扩大测试范围")


def example_4_risk_analysis():
    """示例4: 风险分析"""
    print("\n\n📊 示例4: 投资风险分析")
    print("="*60)
    
    stock_code = "600066"
    target_date = "2025-03-10"
    
    print(f"🎯 分析 {stock_code} 在 {target_date} 的投资风险")
    
    result = predict_price_range(stock_code, target_date, detailed=True)
    
    if result.get('success', False) and result['prediction_results']['has_valid_range']:
        stock_info = result['stock_info']
        price_range = result['prediction_results']['price_range']
        constraints = result['price_constraints']
        
        prev_close = stock_info['prev_close']
        min_price = price_range['min']
        max_price = price_range['max']
        
        print(f"\n📊 风险分析报告:")
        print(f"   股票代码: {stock_info['code']}")
        print(f"   板块类型: {stock_info['board_type']}")
        print(f"   前收盘价: {prev_close:.2f}")
        
        # 收益风险分析
        max_gain = (max_price - prev_close) / prev_close * 100
        max_loss = (min_price - prev_close) / prev_close * 100
        
        print(f"\n💰 收益风险:")
        print(f"   最大潜在收益: {max_gain:.1f}%")
        print(f"   最大潜在亏损: {max_loss:.1f}%")
        print(f"   收益风险比: {abs(max_gain/max_loss):.2f}:1" if max_loss != 0 else "   收益风险比: 无限")
        
        # 价格波动分析
        price_volatility = (max_price - min_price) / prev_close * 100
        print(f"   预期价格波动: {price_volatility:.1f}%")
        
        # 涨跌幅限制分析
        limit_min = constraints['min_price']
        limit_max = constraints['max_price']
        
        print(f"\n📏 涨跌幅限制:")
        print(f"   限制范围: {limit_min:.2f} - {limit_max:.2f}")
        print(f"   预测范围占限制比例: {(max_price-min_price)/(limit_max-limit_min)*100:.1f}%")
        
        # 风险等级评估
        if price_volatility < 3:
            risk_level = "低风险"
        elif price_volatility < 6:
            risk_level = "中等风险"
        else:
            risk_level = "高风险"
        
        print(f"\n⚠️  风险等级: {risk_level}")
        
        # 投资建议
        print(f"\n💡 投资建议:")
        if max_gain > abs(max_loss) * 2:
            print(f"   ✅ 收益风险比较好，可以考虑投资")
        elif max_gain > abs(max_loss):
            print(f"   ⚠️  收益略大于风险，谨慎投资")
        else:
            print(f"   ❌ 风险较大，不建议投资")
            
    else:
        print(f"❌ 无法进行风险分析: {result.get('error', '未找到有效价格范围')}")


def main():
    """主函数 - 运行所有示例"""
    print("🚀 股票价格范围预测功能使用示例")
    print("="*80)
    print("本示例将演示如何使用价格预测模块进行股票投资分析")
    print("="*80)
    
    # 运行所有示例
    try:
        example_1_basic_prediction()
        example_2_batch_analysis()
        example_3_investment_strategy()
        example_4_risk_analysis()
        
        print("\n\n🎉 所有示例运行完成！")
        print("\n💡 使用提示:")
        print("   1. 技术指标条件较为严格，不是每个日期都有投资机会")
        print("   2. 预测结果仅供参考，实际投资需要综合考虑多种因素")
        print("   3. 建议结合其他分析方法和风险管理策略")
        print("   4. 投资有风险，决策需谨慎")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")


if __name__ == "__main__":
    main()
