## 任务
1. 给定股票代码和日期，计算日期前一天的 hdly 指标值和 Kdj_rsi 指标的 J 值，然后计算一下，如果日期当天的 high, low, close, open 在什么范围内，才符合以下条件:
    a. 能使得 hdly 指标值大于 0.1 且当前指标值大于前一个指标值;
    b. kdj_rsi 指标中的 J 值大于前一个 J 值 且 当前 J 值低于 20 且前一个 J 值相对其前 5 天来看是最低点且当前 J 值与前一个 J 值连线角度大于 70 度
    c. 涨跌幅范围在合理范围，即主板股票涨跌幅限制在 10% 以内，科创板在 20% 以内

2. 在任务 1 的基础上，实现指定日期批量执行股票（data 文件夹内的所有股票）预测的功能，该功能另起一个文件实现

## 规范
1. 所有代码在 predict 文件夹内实现
2. 数据可以从根目录的 data 文件夹内读取，数据文件为 [股票代码].csv
3. hdly指标是 hdly/hdly_indicator.py 实现的
4. kdj_rsi指标是 kdj_rsi/kdj_rsi_indicator.py 实现的
