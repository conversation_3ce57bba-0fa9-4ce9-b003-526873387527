#!/usr/bin/env python3
"""
沪深300成分股工具模块

提供沪深300成分股的加载、查询和标注功能
"""

import os
import pandas as pd
from typing import Set, List, Dict, Any


class HS300Utils:
    """沪深300成分股工具类"""
    
    def __init__(self, hs300_csv_path: str = "hs300_stocks.csv"):
        """
        初始化沪深300工具类
        
        参数:
        hs300_csv_path: 沪深300成分股CSV文件路径
        """
        self.hs300_csv_path = hs300_csv_path
        self._hs300_stocks = self._load_hs300_stocks()
    
    def _load_hs300_stocks(self) -> Set[str]:
        """加载沪深300成分股列表"""
        try:
            if not os.path.exists(self.hs300_csv_path):
                print(f"警告: 沪深300成分股文件 {self.hs300_csv_path} 不存在，将无法标注沪深300成分股")
                return set()
            
            hs300_df = pd.read_csv(self.hs300_csv_path)
            if 'code' not in hs300_df.columns:
                print(f"警告: 沪深300成分股文件格式错误，缺少 'code' 列")
                return set()
            
            # 提取股票代码，去掉交易所前缀（如 sh.600000 -> 600000）
            hs300_codes = set()
            for code in hs300_df['code']:
                if isinstance(code, str):
                    # 去掉 sh. 或 sz. 前缀
                    clean_code = code.split('.')[-1] if '.' in code else code
                    hs300_codes.add(clean_code)
            
            print(f"成功加载 {len(hs300_codes)} 只沪深300成分股")
            return hs300_codes
            
        except Exception as e:
            print(f"加载沪深300成分股文件时出错: {e}")
            return set()
    
    def is_hs300_stock(self, stock_code: str) -> bool:
        """
        检查股票是否为沪深300成分股
        
        参数:
        stock_code: 股票代码
        
        返回:
        bool: 是否为沪深300成分股
        """
        # 去掉可能的交易所前缀
        clean_code = stock_code.split('.')[-1] if '.' in stock_code else stock_code
        return clean_code in self._hs300_stocks
    
    def get_hs300_label(self, stock_code: str) -> str:
        """
        获取股票的沪深300标签
        
        参数:
        stock_code: 股票代码
        
        返回:
        str: 沪深300标签
        """
        return "沪深300" if self.is_hs300_stock(stock_code) else "非沪深300"
    
    def get_hs300_badge(self, stock_code: str) -> str:
        """
        获取股票的沪深300徽章（用于HTML显示）
        
        参数:
        stock_code: 股票代码
        
        返回:
        str: 沪深300徽章
        """
        return "✅" if self.is_hs300_stock(stock_code) else "❌"
    
    def annotate_stock_list(self, stock_codes: List[str]) -> List[Dict[str, Any]]:
        """
        为股票列表添加沪深300标注
        
        参数:
        stock_codes: 股票代码列表
        
        返回:
        List[Dict]: 包含沪深300标注的股票信息列表
        """
        annotated_stocks = []
        for code in stock_codes:
            annotated_stocks.append({
                'code': code,
                'is_hs300': self.is_hs300_stock(code),
                'hs300_label': self.get_hs300_label(code),
                'hs300_badge': self.get_hs300_badge(code)
            })
        return annotated_stocks
    
    def format_stock_list_with_hs300(self, stock_codes: List[str], separator: str = ", ") -> str:
        """
        格式化股票列表，包含沪深300标注
        
        参数:
        stock_codes: 股票代码列表
        separator: 分隔符
        
        返回:
        str: 格式化的股票列表字符串
        """
        if not stock_codes:
            return "无符合条件股票"
        
        formatted_stocks = []
        for code in stock_codes:
            hs300_label = self.get_hs300_label(code)
            formatted_stocks.append(f"{code}[{hs300_label}]")
        
        return separator.join(formatted_stocks)
    
    def get_hs300_summary(self, stock_codes: List[str]) -> Dict[str, Any]:
        """
        获取股票列表的沪深300统计摘要
        
        参数:
        stock_codes: 股票代码列表
        
        返回:
        Dict: 包含统计信息的字典
        """
        if not stock_codes:
            return {
                'total_count': 0,
                'hs300_count': 0,
                'non_hs300_count': 0,
                'hs300_ratio': 0.0
            }
        
        hs300_count = sum(1 for code in stock_codes if self.is_hs300_stock(code))
        non_hs300_count = len(stock_codes) - hs300_count
        hs300_ratio = (hs300_count / len(stock_codes)) * 100
        
        return {
            'total_count': len(stock_codes),
            'hs300_count': hs300_count,
            'non_hs300_count': non_hs300_count,
            'hs300_ratio': hs300_ratio
        }


# 创建全局实例，方便其他模块使用
hs300_utils = HS300Utils()


def is_hs300_stock(stock_code: str) -> bool:
    """便捷函数：检查股票是否为沪深300成分股"""
    return hs300_utils.is_hs300_stock(stock_code)


def get_hs300_label(stock_code: str) -> str:
    """便捷函数：获取股票的沪深300标签"""
    return hs300_utils.get_hs300_label(stock_code)


def format_stock_list_with_hs300(stock_codes: List[str], separator: str = ", ") -> str:
    """便捷函数：格式化股票列表，包含沪深300标注"""
    return hs300_utils.format_stock_list_with_hs300(stock_codes, separator)


def get_hs300_summary(stock_codes: List[str]) -> Dict[str, Any]:
    """便捷函数：获取股票列表的沪深300统计摘要"""
    return hs300_utils.get_hs300_summary(stock_codes)


if __name__ == "__main__":
    # 测试代码
    print("沪深300工具模块测试")
    
    # 测试股票代码
    test_codes = ["600000", "000001", "300750", "688981", "123456"]
    
    print(f"\n测试股票代码: {test_codes}")
    
    for code in test_codes:
        is_hs300 = is_hs300_stock(code)
        label = get_hs300_label(code)
        print(f"{code}: {is_hs300} - {label}")
    
    print(f"\n格式化列表: {format_stock_list_with_hs300(test_codes)}")
    
    summary = get_hs300_summary(test_codes)
    print(f"\n统计摘要: {summary}")
