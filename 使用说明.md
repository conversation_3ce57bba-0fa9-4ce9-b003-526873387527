# 股票购买方案计算器 - 使用说明

## 快速开始

1. **安装依赖**
   ```bash
   pip install mootdx
   ```

2. **修改配置**
   编辑 `buy_in.py` 文件第149行和152行：
   ```python
   # 修改股票代码列表
   stock_list = ['000001', '000002', '600036', '600519']
   
   # 修改总资金
   total_capital = 20000
   ```

3. **运行程序**
   ```bash
   python buy_in.py
   ```

## 功能说明

程序会自动：
- 获取指定股票的实时价格
- 计算最优的资金分配方案
- 显示每只股票的购买股数和投入金额
- 最大化资金利用率

## 算法原理

1. **整手交易**: 股数必须是100股的整数倍（1手=100股）
2. **均衡分配**: 将总资金尽量平均分配给所有股票
3. **占比控制**: 确保各股票资金占比相差不超过5%
4. **智能优化**: 优先给占比较低的股票分配剩余资金
5. **最大利用**: 确保资金利用率最大化

## 注意事项

- 股票代码必须是6位数字字符串
- 股数只能是100股的整数倍（按手交易）
- 各股票资金占比会控制在5%差异范围内
- 需要网络连接获取实时数据
- 如果实时数据获取失败，会自动使用最新收盘价
- 程序支持沪深两市股票

## 示例

输入：
- 股票: ['000001', '000002', '600036', '600519']
- 资金: 20000元

输出：
```
================================================================================
股票购买方案
================================================================================
股票代码	价格(元)	股数	投入金额(元)	资金占比(%)
--------------------------------------------------------------------------------
601187		7.18		700	5026.00		25.1%
600016		5.05		1100	5555.00		27.8%
002121		6.00		800	4800.00		24.0%
000531		6.58		700	4606.00		23.0%
--------------------------------------------------------------------------------
总投入金额：19987.00元
剩余资金：13.00元
资金利用率：99.9%
资金占比差异：4.7% (最高27.8% - 最低23.0%)
✓ 资金占比均衡，差异在5%以内
================================================================================
```

## 免责声明

本工具仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。
