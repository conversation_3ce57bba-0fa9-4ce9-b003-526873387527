#!/usr/bin/env python3
"""
专门用于获取缺失的沪深300成分股数据的脚本
基于根目录的 fetch_kline.py，但只获取指定的股票代码
"""

import sys
import logging
import argparse
from pathlib import Path
from typing import List, Set
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import time

# 添加根目录到 Python 路径，以便导入 fetch_kline 模块
root_dir = Path(__file__).parent.parent
sys.path.insert(0, str(root_dir))

try:
    # 导入根目录的 fetch_kline 模块中的函数
    from fetch_kline import (
        _get_kline_tushare, _get_kline_akshare, _get_kline_mootdx,
        _FREQ_MAP, get_kline, validate
    )
except ImportError as e:
    print(f"无法导入 fetch_kline 模块: {e}")
    print("请确保根目录存在 fetch_kline.py 文件")
    sys.exit(1)

import datetime as dt

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("fetch_missing.log", encoding="utf-8"),
    ],
)
logger = logging.getLogger("fetch_missing_hs300")


def get_missing_codes(hs300_codes: List[str], data_dir: Path) -> List[str]:
    """获取缺失数据的股票代码"""
    if not data_dir.exists():
        return hs300_codes
    
    existing_files = list(data_dir.glob("*.csv"))
    existing_codes = {f.stem for f in existing_files}
    
    missing_codes = [code for code in hs300_codes if code not in existing_codes]
    return missing_codes


def _parse_date(date_str: str) -> str:
    """解析日期字符串"""
    if date_str.lower() == "today":
        return dt.date.today().strftime("%Y%m%d")
    return date_str


def fetch_single_stock(code: str, datasource: str, frequency: int, start: str, end: str, out_dir: Path) -> bool:
    """获取单只股票的数据"""
    try:
        csv_path = out_dir / f"{code}.csv"

        # 如果文件已存在，跳过
        if csv_path.exists():
            logger.debug("股票 %s 数据已存在，跳过", code)
            return True

        # 使用 fetch_kline 模块的通用接口
        df = get_kline(code, start, end, "qfq", datasource, frequency)

        if df is None or df.empty:
            logger.warning("股票 %s 未获取到数据", code)
            return False

        # 数据验证
        df = validate(df)

        # 保存数据
        df.to_csv(csv_path, index=False, encoding="utf-8")
        logger.info("股票 %s 数据保存成功，共 %d 条记录", code, len(df))
        return True

    except Exception as e:
        logger.error("获取股票 %s 数据失败: %s", code, e)
        return False


def fetch_missing_data_batch(missing_codes: List[str], datasource: str, frequency: int, 
                           start: str, end: str, out_dir: Path, workers: int = 3) -> int:
    """批量获取缺失股票的数据"""
    if not missing_codes:
        logger.info("没有缺失的股票数据")
        return 0
    
    logger.info("开始获取 %d 只股票的数据，使用 %d 个线程", len(missing_codes), workers)
    
    success_count = 0
    failed_codes = []
    
    with ThreadPoolExecutor(max_workers=workers) as executor:
        # 提交所有任务
        future_to_code = {
            executor.submit(fetch_single_stock, code, datasource, frequency, start, end, out_dir): code
            for code in missing_codes
        }
        
        # 使用 tqdm 显示进度
        with tqdm(total=len(missing_codes), desc="获取股票数据") as pbar:
            for future in as_completed(future_to_code):
                code = future_to_code[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                    else:
                        failed_codes.append(code)
                except Exception as e:
                    logger.error("处理股票 %s 时出现异常: %s", code, e)
                    failed_codes.append(code)
                finally:
                    pbar.update(1)
                    # 添加小延迟避免请求过于频繁
                    time.sleep(0.1)
    
    logger.info("数据获取完成：成功 %d 只，失败 %d 只", success_count, len(failed_codes))
    if failed_codes:
        logger.warning("获取失败的股票: %s", ", ".join(failed_codes[:10]) + ("..." if len(failed_codes) > 10 else ""))
    
    return success_count


def main():
    parser = argparse.ArgumentParser(description="获取缺失的沪深300成分股数据")
    parser.add_argument("--codes", help="股票代码列表文件路径，默认使用 hs300_codes.txt")
    parser.add_argument("--datasource", choices=["tushare", "akshare", "mootdx"], default="akshare", help="数据源")
    parser.add_argument("--frequency", type=int, choices=list(_FREQ_MAP.keys()), default=4, help="K线频率")
    parser.add_argument("--start", default="20200101", help="开始日期 YYYYMMDD")
    parser.add_argument("--end", default="today", help="结束日期 YYYYMMDD")
    parser.add_argument("--out", help="输出目录，默认为根目录的 data 文件夹")
    parser.add_argument("--workers", type=int, default=3, help="并发线程数")
    parser.add_argument("--force", action="store_true", help="强制重新获取所有数据")
    args = parser.parse_args()
    
    # 设置默认路径
    if args.codes is None:
        args.codes = Path(__file__).parent / "hs300_codes.txt"
    else:
        args.codes = Path(args.codes)
    
    if args.out is None:
        args.out = root_dir / "data"
    else:
        args.out = Path(args.out)
    
    # 读取股票代码列表
    if not args.codes.exists():
        logger.error("股票代码文件不存在: %s", args.codes)
        sys.exit(1)
    
    with open(args.codes, "r", encoding="utf-8") as f:
        all_codes = [line.strip() for line in f if line.strip()]
    
    logger.info("读取到 %d 只股票代码", len(all_codes))
    
    # 创建输出目录
    args.out.mkdir(parents=True, exist_ok=True)
    
    # 确定需要获取的股票代码
    if args.force:
        target_codes = all_codes
        logger.info("强制模式：将重新获取所有 %d 只股票的数据", len(target_codes))
    else:
        target_codes = get_missing_codes(all_codes, args.out)
        logger.info("发现 %d 只股票缺少数据", len(target_codes))
    
    if not target_codes:
        logger.info("所有股票数据都已存在")
        return
    
    # 解析日期
    start = _parse_date(args.start)
    end = _parse_date(args.end)
    
    logger.info("开始获取数据：数据源=%s，频率=%s，日期=%s到%s", 
                args.datasource, _FREQ_MAP[args.frequency], start, end)
    
    # 批量获取数据
    success_count = fetch_missing_data_batch(
        target_codes, args.datasource, args.frequency, 
        start, end, args.out, args.workers
    )
    
    logger.info("任务完成：成功获取 %d/%d 只股票的数据", success_count, len(target_codes))


if __name__ == "__main__":
    main()
