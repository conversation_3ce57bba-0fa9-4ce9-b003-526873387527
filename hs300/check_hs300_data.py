#!/usr/bin/env python3
"""
检查根目录 data 文件夹中沪深300成分股的数据覆盖情况
如果缺少数据，则使用 fetch_kline.py 补充
"""

import sys
import logging
from pathlib import Path
from typing import List, Set
import subprocess

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
    ],
)
logger = logging.getLogger("check_hs300_data")


def get_hs300_constituents() -> List[str]:
    """获取沪深300成分股列表"""
    codes_file = Path(__file__).parent / "hs300_codes.txt"
    
    if not codes_file.exists():
        logger.error("沪深300成分股文件 %s 不存在", codes_file)
        return []
    
    try:
        with open(codes_file, "r", encoding="utf-8") as f:
            codes = [line.strip() for line in f if line.strip()]
        logger.info("加载沪深300成分股，共 %d 只股票", len(codes))
        return codes
    except Exception as e:
        logger.error("读取沪深300成分股文件失败: %s", e)
        return []


def check_data_coverage(data_dir: Path, hs300_codes: List[str]) -> tuple[Set[str], Set[str]]:
    """检查数据覆盖情况"""
    if not data_dir.exists():
        logger.warning("数据目录 %s 不存在", data_dir)
        return set(), set(hs300_codes)
    
    # 获取现有的数据文件
    existing_files = list(data_dir.glob("*.csv"))
    existing_codes = {f.stem for f in existing_files}
    
    # 计算覆盖情况
    hs300_set = set(hs300_codes)
    covered_codes = existing_codes & hs300_set
    missing_codes = hs300_set - existing_codes
    
    logger.info("数据目录: %s", data_dir.absolute())
    logger.info("总文件数: %d", len(existing_files))
    logger.info("沪深300成分股总数: %d", len(hs300_codes))
    logger.info("已有数据的沪深300股票: %d", len(covered_codes))
    logger.info("缺少数据的沪深300股票: %d", len(missing_codes))
    
    if missing_codes:
        logger.info("缺少数据的股票代码: %s", ", ".join(sorted(missing_codes)[:10]) + ("..." if len(missing_codes) > 10 else ""))
    
    return covered_codes, missing_codes


def fetch_missing_data(missing_codes: Set[str], data_dir: Path, auto_fetch: bool = False):
    """使用 fetch_kline.py 获取缺少的数据"""
    if not missing_codes:
        logger.info("所有沪深300成分股数据都已存在，无需补充")
        return True

    logger.info("发现 %d 只股票缺少数据", len(missing_codes))

    if not auto_fetch:
        logger.info("如需自动补充数据，请使用 --auto-fetch 参数")
        return False

    logger.info("开始自动补充 %d 只股票的数据...", len(missing_codes))

    # 检查根目录的 fetch_kline.py 脚本
    root_dir = Path(__file__).parent.parent
    fetch_script = root_dir / "fetch_kline.py"

    if not fetch_script.exists():
        logger.error("找不到 fetch_kline.py 脚本: %s", fetch_script)
        return False

    # 创建临时的股票代码文件
    temp_codes_file = root_dir / "temp_hs300_missing_codes.txt"
    success = False

    try:
        # 写入缺失的股票代码
        with open(temp_codes_file, "w", encoding="utf-8") as f:
            for code in sorted(missing_codes):
                f.write(f"{code}\n")

        logger.info("创建临时股票代码文件: %s", temp_codes_file)

        # 构建命令来获取特定股票的数据
        # 我们需要修改 fetch_kline.py 来支持从文件读取股票代码
        # 或者使用现有的参数来获取数据

        import os
        import sys

        # 切换到根目录
        original_cwd = os.getcwd()
        os.chdir(root_dir)

        try:
            # 使用专门的脚本来获取缺失的沪深300数据
            import subprocess

            # 使用我们的专门脚本
            fetch_script = Path(__file__).parent / "fetch_missing_hs300_data.py"

            cmd = [
                sys.executable, str(fetch_script),
                "--out", str(data_dir),
                "--start", "20200101",
                "--end", "today",
                "--datasource", "akshare",  # 使用免费的 akshare 数据源
                "--workers", "3"
            ]

            logger.info("执行命令: %s", " ".join(cmd))

            # 运行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时

            if result.returncode == 0:
                logger.info("数据获取完成")
                logger.info("输出: %s", result.stdout[-500:] if result.stdout else "无输出")  # 显示最后500字符
                success = True
            else:
                logger.error("数据获取失败，返回码: %d", result.returncode)
                logger.error("错误输出: %s", result.stderr)
                if result.stdout:
                    logger.info("标准输出: %s", result.stdout[-500:])

        except subprocess.TimeoutExpired:
            logger.error("数据获取超时（30分钟）")
        except Exception as e:
            logger.error("数据获取过程中出现异常: %s", e)
        finally:
            os.chdir(original_cwd)
            if str(root_dir) in sys.path:
                sys.path.remove(str(root_dir))

    finally:
        # 清理临时文件
        if temp_codes_file.exists():
            temp_codes_file.unlink()
            logger.info("清理临时文件: %s", temp_codes_file)

    return success


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="检查沪深300成分股数据覆盖情况并可选择自动补充")
    parser.add_argument("--auto-fetch", action="store_true", help="自动补充缺失的数据")
    parser.add_argument("--report-only", action="store_true", help="仅生成报告，不提供补充建议")
    args = parser.parse_args()

    # 获取沪深300成分股列表
    hs300_codes = get_hs300_constituents()
    if not hs300_codes:
        logger.error("无法获取沪深300成分股列表")
        sys.exit(1)

    # 检查根目录 data 文件夹
    root_dir = Path(__file__).parent.parent
    data_dir = root_dir / "data"

    # 检查数据覆盖情况
    covered_codes, missing_codes = check_data_coverage(data_dir, hs300_codes)

    # 处理缺失数据
    if missing_codes:
        if args.auto_fetch:
            logger.info("\n=== 自动补充数据 ===")
            success = fetch_missing_data(missing_codes, data_dir, auto_fetch=True)
            if success:
                # 重新检查数据覆盖情况
                logger.info("\n=== 重新检查数据覆盖情况 ===")
                covered_codes, missing_codes = check_data_coverage(data_dir, hs300_codes)
                if not missing_codes:
                    logger.info("✅ 所有沪深300成分股数据补充完成！")
                else:
                    logger.warning("⚠️  仍有 %d 只股票数据缺失", len(missing_codes))
            else:
                logger.error("❌ 数据补充失败")
        elif not args.report_only:
            logger.info("\n=== 数据补充建议 ===")
            logger.info("发现 %d 只沪深300成分股缺少数据", len(missing_codes))
            logger.info("可以使用以下方式补充数据：")
            logger.info("\n1. 自动补充（推荐）：")
            logger.info("python check_hs300_data.py --auto-fetch")
            logger.info("\n2. 手动补充：")
            logger.info("cd %s", root_dir.absolute())
            logger.info("python fetch_kline.py --out ./data --start 20200101 --end today")
            logger.info("\n或者使用 akshare 数据源：")
            logger.info("python fetch_kline.py --datasource akshare --out ./data --start 20200101")
    else:
        logger.info("\n✅ 所有沪深300成分股数据都已存在！")
    
    # 生成数据覆盖报告
    report_file = Path(__file__).parent / "hs300_data_coverage_report.txt"
    with open(report_file, "w", encoding="utf-8") as f:
        f.write("沪深300成分股数据覆盖报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"检查时间: {Path(__file__).stat().st_mtime}\n")
        f.write(f"数据目录: {data_dir.absolute()}\n")
        f.write(f"沪深300成分股总数: {len(hs300_codes)}\n")
        f.write(f"已有数据股票数: {len(covered_codes)}\n")
        f.write(f"缺少数据股票数: {len(missing_codes)}\n")
        f.write(f"数据覆盖率: {len(covered_codes)/len(hs300_codes)*100:.1f}%\n\n")
        
        if missing_codes:
            f.write("缺少数据的股票代码:\n")
            for code in sorted(missing_codes):
                f.write(f"  {code}\n")
        
        if covered_codes:
            f.write("\n已有数据的股票代码:\n")
            for code in sorted(covered_codes):
                f.write(f"  {code}\n")
    
    logger.info("数据覆盖报告已保存到: %s", report_file.absolute())


if __name__ == "__main__":
    main()
