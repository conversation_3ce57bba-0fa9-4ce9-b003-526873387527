#!/usr/bin/env python3
"""
获取沪深300成分股列表
使用 baostock 库获取沪深300成分股列表并保存为 CSV 文件
"""

import baostock as bs
import pandas as pd
import logging
import sys
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
    ],
)
logger = logging.getLogger("hs300_stocks")


def get_hs300_stocks():
    """获取沪深300成分股列表"""
    # 登录系统
    lg = bs.login()
    if lg.error_code != '0':
        logger.error(f"登录失败: {lg.error_msg}")
        return None
    
    logger.info("成功登录 baostock")
    
    try:
        # 获取沪深300成分股
        rs = bs.query_hs300_stocks()
        if rs.error_code != '0':
            logger.error(f"获取沪深300成分股失败: {rs.error_msg}")
            return None
        
        # 打印结果集
        hs300_stocks = []
        while (rs.error_code == '0') & rs.next():
            # 获取一条记录，将记录合并在一起
            hs300_stocks.append(rs.get_row_data())
        
        # 转换为DataFrame
        result = pd.DataFrame(hs300_stocks, columns=rs.fields)
        logger.info(f"成功获取沪深300成分股，共 {len(result)} 只股票")
        
        return result
        
    finally:
        # 登出系统
        bs.logout()
        logger.info("已登出 baostock")


def save_hs300_stocks(output_file="hs300_stocks.csv"):
    """获取并保存沪深300成分股列表"""
    stocks_df = get_hs300_stocks()
    
    if stocks_df is None or stocks_df.empty:
        logger.error("未能获取到沪深300成分股数据")
        return False
    
    # 确保输出目录存在
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 保存到CSV文件
    stocks_df.to_csv(output_path, index=False, encoding='utf-8')
    logger.info(f"沪深300成分股列表已保存到: {output_path.absolute()}")
    
    # 显示数据预览
    print(f"\n沪深300成分股数据预览：")
    print(f"股票数量：{len(stocks_df)}")
    print(f"数据列：{list(stocks_df.columns)}")
    print("\n前10条数据：")
    print(stocks_df.head(10))
    
    # 提取股票代码列表（去掉交易所前缀）
    if 'code' in stocks_df.columns:
        # 去掉 sh. 和 sz. 前缀
        stock_codes = []
        for code in stocks_df['code']:
            if code.startswith('sh.'):
                clean_code = code[3:]  # 去掉 'sh.'
            elif code.startswith('sz.'):
                clean_code = code[3:]  # 去掉 'sz.'
            else:
                clean_code = code
            stock_codes.append(clean_code)

        codes_file = output_path.parent / "hs300_codes.txt"
        with open(codes_file, 'w', encoding='utf-8') as f:
            for code in stock_codes:
                f.write(f"{code}\n")
        logger.info(f"股票代码列表已保存到: {codes_file.absolute()}")

        return stock_codes
    
    return True


if __name__ == "__main__":
    # 保存到 hs300 目录下
    save_hs300_stocks("hs300_stocks.csv")
