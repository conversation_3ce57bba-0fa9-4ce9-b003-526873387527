# 沪深300成分股交易系统

本目录包含专门针对沪深300成分股的股票数据获取和选股系统。

## 文件说明

### 数据文件
- `hs300_stocks.csv` - 沪深300成分股完整信息（包含股票代码、名称等）
- `hs300_codes.txt` - 沪深300成分股代码列表（纯代码，每行一个）
- `../data/` - 股票历史K线数据存储目录（根目录的 data 文件夹）
- `hs300_data_coverage_report.txt` - 沪深300数据覆盖情况报告

### 脚本文件
- `get_hs300_stocks.py` - 获取沪深300成分股列表的脚本
- `check_hs300_data.py` - 检查沪深300成分股数据覆盖情况并可自动补充缺失数据
- `fetch_missing_hs300_data.py` - 专门用于获取缺失的沪深300成分股数据
- `hs300_select_stock.py` - 基于沪深300成分股的选股脚本
- `hs300_configs.json` - 选股策略配置文件

## 使用方法

### 1. 获取沪深300成分股列表
```bash
python get_hs300_stocks.py
```
这将生成 `hs300_stocks.csv` 和 `hs300_codes.txt` 文件。

### 2. 检查并补充历史K线数据
```bash
# 检查沪深300成分股数据覆盖情况
python check_hs300_data.py

# 自动补充缺失的数据（推荐）
python check_hs300_data.py --auto-fetch

# 仅生成报告，不显示补充建议
python check_hs300_data.py --report-only

# 手动补充特定股票的数据
python fetch_missing_hs300_data.py --datasource akshare --workers 3

# 强制重新获取所有沪深300数据
python fetch_missing_hs300_data.py --force --datasource akshare
```

### 3. 运行选股策略
```bash
# 使用沪深300成分股运行选股（默认使用根目录 data 文件夹）
python hs300_select_stock.py --config ./hs300_configs.json --tickers hs300

# 指定特定日期
python hs300_select_stock.py --date 2025-07-29 --tickers hs300

# 使用数据目录中的所有股票
python hs300_select_stock.py --tickers all

# 手动指定数据目录
python hs300_select_stock.py --data-dir ../data --tickers hs300
```

## 配置说明

### hs300_configs.json
包含以下选股策略：
- **少妇战法** (BBIKDJSelector) - 基于BBI和KDJ指标的选股策略
- **补票战法** (BBIShortLongSelector) - 基于BBI短长期对比的选股策略  
- **TePu战法** (BreakoutVolumeKDJSelector) - 基于突破、成交量和KDJ的选股策略
- **填坑战法** (PeakKDJSelector) - 基于峰值和KDJ的选股策略

可以通过修改配置文件中的 `activate` 字段来启用/禁用特定策略，或调整 `params` 中的参数。

## 数据覆盖情况

当前沪深300成分股数据覆盖情况：
- 总股票数：300只
- 已有数据：253只 (84.3%)
- 缺少数据：47只

运行 `python check_hs300_data.py` 可以查看详细的数据覆盖报告。

### 自动补充数据
系统提供了自动补充缺失数据的功能：
```bash
# 一键自动补充所有缺失的沪深300数据
python check_hs300_data.py --auto-fetch
```
该功能会：
1. 自动检测缺失的股票数据
2. 使用 akshare 数据源获取缺失数据
3. 保存到根目录的 data 文件夹
4. 重新检查数据覆盖情况

## 注意事项

1. 首次运行需要先获取沪深300成分股列表
2. 数据存储在根目录的 `data` 文件夹中，所有脚本共享同一数据源
3. 选股功能会自动使用根目录 `data` 文件夹中的数据
4. 如果发现数据缺失，使用根目录的 `fetch_kline.py` 补充数据
5. 数据源选择：
   - `tushare` - 需要token，数据质量高
   - `akshare` - 免费，但可能有限制
   - `mootdx` - 通达信数据源

## 依赖库

确保安装了以下Python库：
```bash
pip install baostock pandas akshare tushare mootdx tqdm
```
