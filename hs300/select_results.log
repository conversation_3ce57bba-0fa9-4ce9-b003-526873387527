2025-07-29 14:12:04,595 [INFO] 加载沪深300成分股，共 300 只股票
2025-07-29 14:17:00,604 [INFO] 加载沪深300成分股，共 300 只股票
2025-07-29 14:18:22,720 [INFO] 加载沪深300成分股，共 300 只股票
2025-07-29 14:27:19,473 [INFO] 加载沪深300成分股，共 300 只股票
2025-07-29 14:27:19,477 [INFO] 沪深300成分股中有数据的股票: 300 只
2025-07-29 14:27:19,813 [INFO] 未指定 --date，使用最近日期 2025-07-29
2025-07-29 14:27:19,813 [ERROR] 跳过配置 {'class': 'BBIKDJSelector', 'alias': '少妇战法', 'activate': True, 'params': {'j_threshold': 0, 'bbi_min_window': 20, 'max_window': 60, 'price_range_pct': 0.5, 'bbi_q_threshold': 0.1, 'j_q_threshold': 0.01}}：无法加载 Selector.BBIKDJSelector: No module named 'Selector'
2025-07-29 14:27:19,814 [ERROR] 跳过配置 {'class': 'BBIShortLongSelector', 'alias': '补票战法', 'activate': True, 'params': {'n_short': 3, 'n_long': 21, 'm': 3, 'bbi_min_window': 2, 'max_window': 60, 'bbi_q_threshold': 0.2}}：无法加载 Selector.BBIShortLongSelector: No module named 'Selector'
2025-07-29 14:27:19,814 [ERROR] 跳过配置 {'class': 'BreakoutVolumeKDJSelector', 'alias': 'TePu战法', 'activate': True, 'params': {'j_threshold': 1, 'j_q_threshold': 0.1, 'up_threshold': 3.0, 'volume_threshold': 0.6667, 'offset': 15, 'max_window': 60, 'price_range_pct': 0.5}}：无法加载 Selector.BreakoutVolumeKDJSelector: No module named 'Selector'
2025-07-29 14:27:19,814 [ERROR] 跳过配置 {'class': 'PeakKDJSelector', 'alias': '填坑战法', 'activate': True, 'params': {'j_threshold': 10, 'max_window': 100, 'fluc_threshold': 0.03, 'j_q_threshold': 0.1, 'gap_threshold': 0.2}}：无法加载 Selector.PeakKDJSelector: No module named 'Selector'
2025-07-29 14:27:58,757 [INFO] 加载沪深300成分股，共 300 只股票
2025-07-29 14:27:58,761 [INFO] 沪深300成分股中有数据的股票: 300 只
2025-07-29 14:27:59,096 [INFO] 未指定 --date，使用最近日期 2025-07-29
2025-07-29 14:27:59,097 [ERROR] 跳过配置 {'class': 'BBIKDJSelector', 'alias': '少妇战法', 'activate': True, 'params': {'j_threshold': 0, 'bbi_min_window': 20, 'max_window': 60, 'price_range_pct': 0.5, 'bbi_q_threshold': 0.1, 'j_q_threshold': 0.01}}：无法加载 Selector.BBIKDJSelector: No module named 'Selector'
2025-07-29 14:27:59,097 [ERROR] 跳过配置 {'class': 'BBIShortLongSelector', 'alias': '补票战法', 'activate': True, 'params': {'n_short': 3, 'n_long': 21, 'm': 3, 'bbi_min_window': 2, 'max_window': 60, 'bbi_q_threshold': 0.2}}：无法加载 Selector.BBIShortLongSelector: No module named 'Selector'
2025-07-29 14:27:59,097 [ERROR] 跳过配置 {'class': 'BreakoutVolumeKDJSelector', 'alias': 'TePu战法', 'activate': True, 'params': {'j_threshold': 1, 'j_q_threshold': 0.1, 'up_threshold': 3.0, 'volume_threshold': 0.6667, 'offset': 15, 'max_window': 60, 'price_range_pct': 0.5}}：无法加载 Selector.BreakoutVolumeKDJSelector: No module named 'Selector'
2025-07-29 14:27:59,097 [ERROR] 跳过配置 {'class': 'PeakKDJSelector', 'alias': '填坑战法', 'activate': True, 'params': {'j_threshold': 10, 'max_window': 100, 'fluc_threshold': 0.03, 'j_q_threshold': 0.1, 'gap_threshold': 0.2}}：无法加载 Selector.PeakKDJSelector: No module named 'Selector'
2025-07-29 14:28:26,384 [INFO] 加载沪深300成分股，共 300 只股票
2025-07-29 14:28:26,388 [INFO] 沪深300成分股中有数据的股票: 300 只
2025-07-29 14:28:26,728 [INFO] 未指定 --date，使用最近日期 2025-07-29
2025-07-29 14:28:29,285 [INFO] 
2025-07-29 14:28:29,285 [INFO] ============== 选股结果 [少妇战法] ==============
2025-07-29 14:28:29,285 [INFO] 交易日: 2025-07-29
2025-07-29 14:28:29,285 [INFO] 符合条件股票数: 2
2025-07-29 14:28:29,285 [INFO] 601668, 000651
2025-07-29 14:28:29,683 [INFO] 
2025-07-29 14:28:29,683 [INFO] ============== 选股结果 [补票战法] ==============
2025-07-29 14:28:29,683 [INFO] 交易日: 2025-07-29
2025-07-29 14:28:29,683 [INFO] 符合条件股票数: 0
2025-07-29 14:28:29,683 [INFO] 无符合条件股票
2025-07-29 14:28:29,912 [INFO] 
2025-07-29 14:28:29,912 [INFO] ============== 选股结果 [TePu战法] ==============
2025-07-29 14:28:29,912 [INFO] 交易日: 2025-07-29
2025-07-29 14:28:29,912 [INFO] 符合条件股票数: 0
2025-07-29 14:28:29,912 [INFO] 无符合条件股票
2025-07-29 14:28:30,175 [INFO] 
2025-07-29 14:28:30,175 [INFO] ============== 选股结果 [填坑战法] ==============
2025-07-29 14:28:30,175 [INFO] 交易日: 2025-07-29
2025-07-29 14:28:30,176 [INFO] 符合条件股票数: 1
2025-07-29 14:28:30,176 [INFO] 603296
